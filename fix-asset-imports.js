const fs = require('fs');
const path = require('path');

// Function to recursively find all TypeScript and JavaScript files
function findFiles(dir, extensions = ['.ts', '.tsx', '.js', '.jsx']) {
  let results = [];
  const list = fs.readdirSync(dir);
  
  list.forEach(file => {
    const filePath = path.join(dir, file);
    const stat = fs.statSync(filePath);
    
    if (stat && stat.isDirectory()) {
      // Skip node_modules, .next, and other build directories
      if (!['node_modules', '.next', 'dist', 'build', '.git'].includes(file)) {
        results = results.concat(findFiles(filePath, extensions));
      }
    } else {
      const ext = path.extname(file);
      if (extensions.includes(ext)) {
        results.push(filePath);
      }
    }
  });
  
  return results;
}

// Function to fix asset imports in a file
function fixAssetImports(filePath) {
  try {
    let content = fs.readFileSync(filePath, 'utf8');
    let modified = false;

    // Pattern to match imports from /Assets/
    const importPattern = /import\s+(\w+)\s+from\s+['"]\/Assets\/([^'"]+)['"]/g;

    // Replace with string constants instead of imports
    content = content.replace(importPattern, (match, varName, assetPath) => {
      modified = true;
      console.log(`Fixing import in ${filePath}: ${match}`);
      return `const ${varName} = '/Assets/${assetPath}'`;
    });

    if (modified) {
      fs.writeFileSync(filePath, content);
      console.log(`Updated: ${filePath}`);
    }

    return modified;
  } catch (error) {
    console.error(`Error processing ${filePath}:`, error.message);
    return false;
  }
}

// Main execution
console.log('Starting asset import fixes...');

const projectRoot = '.';
const files = findFiles(projectRoot);

let totalFixed = 0;
files.forEach(file => {
  if (fixAssetImports(file)) {
    totalFixed++;
  }
});

console.log(`\nCompleted! Fixed imports in ${totalFixed} files.`);

console.log('Asset import fixes completed!');
