// Default placeholder image for when images are not available
export const defaultImage = '/Assets/images/personal/pro-pic.png';

// API endpoints
export const API_ENDPOINTS = {
  BIO: '/api/bio',
  USER: '/api/user',
  SKILL: '/api/skill',
  PROJECT: '/api/project',
  EDUCATION: '/api/education',
  COMPANY: '/api/company',
  CONTACT: '/api/contact',
  CONFIG: '/api/config',
  INTEREST: '/api/interest',
  SOCIAL_MEDIA: '/api/social-media',
} as const;

// Common constants
export const CONSTANTS = {
  DEFAULT_TIMEOUT: 10000,
  MAX_RETRIES: 3,
  ITEMS_PER_PAGE: 10,
} as const;

// Type definitions for common data structures
export interface BaseEntity {
  id: string;
  _id?: string;
  createdAt?: string;
  updatedAt?: string;
}

export interface ImageEntity extends BaseEntity {
  url: string;
  alt?: string;
  width?: number;
  height?: number;
}

export interface SocialMediaLink {
  platform: string;
  url: string;
  username?: string;
  icon?: string;
}

// Error messages
export const ERROR_MESSAGES = {
  NETWORK_ERROR: 'Network error occurred. Please try again.',
  NOT_FOUND: 'Resource not found.',
  SERVER_ERROR: 'Server error occurred. Please try again later.',
  VALIDATION_ERROR: 'Please check your input and try again.',
} as const;

// Success messages
export const SUCCESS_MESSAGES = {
  DATA_LOADED: 'Data loaded successfully.',
  FORM_SUBMITTED: 'Form submitted successfully.',
  EMAIL_SENT: 'Email sent successfully.',
} as const;
