// Core data types
export interface BaseEntity {
  id: string;
  _id?: string;
  createdAt?: string;
  updatedAt?: string;
}

// User types
export interface User extends BaseEntity {
  name?: string;
  email?: string;
  phone?: string;
  profileImage?: string;
  password?: string;
}

// Company types
export interface Company extends BaseEntity {
  name?: string;
  designation?: string;
  address?: string;
  country?: string;
  website?: string;
}

// Skill types
export interface Skill extends BaseEntity {
  name: string;
  logo?: string;
  description?: string;
  percentage?: number;
  tags?: string[];
  category?: string;
}

// Project types
export interface Project extends BaseEntity {
  name: string;
  description?: string;
  url?: string;
  logos?: string[];
  startDate?: Date | string;
  endDate?: Date | string;
  isWorking?: boolean;
  skill?: Skill[];
  skills?: Skill[];
  skillsId?: string;
}

// Experience types
export interface Experience extends BaseEntity {
  designation: string;
  name: string;
  company?: string;
  logo?: string;
  location?: string;
  startedAt: Date | string;
  endedAt?: Date | string | null;
  startDate?: Date | string;
  endDate?: Date | string | null;
  description?: string;
  companyDescription?: string;
  companyWebsite?: string;
  isCurrent?: boolean;
}

// Education types
export interface Education extends BaseEntity {
  degree: string;
  name: string;
  institution?: string;
  logo?: string;
  location?: string;
  startedAt: Date | string;
  endedAt?: Date | string;
  startDate?: Date | string;
  endDate?: Date | string;
  description?: string;
}

// Contact types
export interface ContactItem {
  name: string;
  link: string;
  type?: string;
  active: boolean;
}

export interface ContactFormData {
  name: string;
  email: string;
  title?: string;
  message: string;
  phone?: string;
}

// Social Media types
export interface SocialMedia {
  name?: string;
  platform?: string;
  url?: string;
  link?: string;
  active?: boolean;
  type?: string;
}

// About section types
export interface FileGroup {
  [fileName: string]: string;
}

export interface Folder {
  title: string;
  description: string;
  files?: FileGroup;
}

export interface Section {
  title: string;
  icon: string;
  info: Record<string, Folder>;
}

export interface AboutSections {
  [key: string]: Section;
}

export interface About {
  sections: AboutSections;
}

// Contact data types
export interface ContactData {
  direct: {
    title: string;
    sources: string[];
  };
  social: {
    [platform: string]: {
      title: string;
      url: string;
      user: string;
    };
  };
  find_me_also_in: {
    title: string;
    sources: {
      title: string;
      url: string;
      user: string;
    }[];
  };
}

// Gist types
export interface GistData {
  id: string;
  owner?: {
    login: string;
    avatar_url: string;
  };
  description?: string;
  html_url?: string;
  comments_url?: string;
  files?: {
    [filename: string]: {
      content: string;
      language: string;
    };
  };
}

export interface Gists {
  [key: number]: string;
}

// Common data types
export interface Config {
  githubURL?: string;
  image?: string;
}

export interface CommonData {
  socialMedias?: SocialMedia[];
  user?: User;
  company?: Company;
  totalExperience?: string;
  config?: Config;
}

// Component prop types
export interface ComponentProps {
  children?: React.ReactNode;
  className?: string;
}

// API Response types
export interface ApiResponse<T = any> {
  data?: T;
  statusCode?: number;
  message?: string;
  error?: string;
}

// Form types
export interface FormProps {
  name: string;
  email: string;
  title?: string;
  message: string;
  configData?: any;
  setName: (name: string) => void;
  setTitle?: (title: string) => void;
  setEmail: (email: string) => void;
  setMessage: (message: string) => void;
}

// Section keys for about page
export type SectionKey = 'professional-info' | 'personal-info';

// Date formatting options
export interface DateFormatOptions {
  year: 'numeric' | '2-digit';
  month: 'numeric' | '2-digit' | 'long' | 'short' | 'narrow';
  day?: 'numeric' | '2-digit';
}

// Event handler types
export type EventHandler<T = HTMLElement> = (event: React.SyntheticEvent<T>) => void;
export type ChangeHandler<T = HTMLInputElement> = (event: React.ChangeEvent<T>) => void;
export type ClickHandler<T = HTMLElement> = (event: React.MouseEvent<T>) => void;

// Navigation types
export interface NavItem {
  name: string;
  href: string;
  active?: boolean;
}

// Image slideshow types
export interface ImageSlideshowProps {
  images?: string[];
  interval?: number;
  skills?: Skill[];
}

// Progress bar types
export interface ProgressBarProps {
  value?: number;
  max?: number;
  className?: string;
}
