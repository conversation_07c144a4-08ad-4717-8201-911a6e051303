// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

// Looking for ways to speed up your queries, or scale easily with your serverless or edge functions?
// Try Prisma Accelerate: https://pris.ly/cli/accelerate-init

generator client {
  provider = "prisma-client-js"
  output   = "../app/generated/prisma"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id        String   @id @default(cuid()) @map("_id")
  name      String?
  email     String?  @unique
  password  String?
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

model Experience {
  id          String    @id @default(cuid()) @map("_id")
  designation String
  company     String
  logo        String?
  location    String?
  startDate   DateTime
  endDate     DateTime?
  description String?
  isCurrent   Boolean   @default(false)
}

model Skills {
  id          String     @id @default(cuid()) @map("_id")
  name        String
  logo        String?
  description String?
  percentage  Int?
  tags        String[]
  category    String?
  Projects    Projects[]
}

model Projects {
  id          String    @id @default(cuid()) @map("_id")
  name        String
  description String?
  url         String?
  logos       String[]
  startDate   DateTime?
  endDate     DateTime?
  isWorking   Boolean   @default(false)
  skillsId    String
  skills      Skills    @relation(fields: [skillsId], references: [id])
}

model Education {
  id          String    @id @default(cuid()) @map("_id")
  degree      String
  institution String
  logo        String?
  location    String?
  startDate   DateTime
  endDate     DateTime?
  description String?
}

model Contact {
  id        String   @id @default(cuid()) @map("_id")
  name      String
  email     String
  message   String
  phone     String?
  urls      Json[]
  createdAt DateTime @default(now())
}
