/*
 * Filename: /home/<USER>/WorkStation/myportfolio/app/aboutme/page.jsx
 * Path: /home/<USER>/WorkStation/myportfolio
 * Created Date: Friday, July 14th 2023, 8:31:49 pm
 * Author: <PERSON><PERSON><PERSON>
 *
 * Copyright (c) 2023 Tanzi<PERSON>
 */

"use client";
import {useEffect, useState} from 'react';
import './index.css';

import LeftSideBox from '@/components/AboutMe/LeftSideBox/LeftSideBox';
import RightSideBox from '@/components/AboutMe/RightSideBox/RightSideBox';
import AboutMeSideMenu from '@/components/AboutMeSideMenu/AboutMeSideMenu';
import { about, contact, gists } from './data';

const AboutMe = () => {
  const [currentSection, setCurrentSection] = useState("professional-info");
  const [folder, setFolder] = useState("experience");
  const [loading, setLoading] = useState(true);

  const focusCurrentSection = (section) => {
    setCurrentSection(about.sections[section].title);
    setFolder(Object.keys(about.sections[section].info)[0]);

    const foldersElement = document.getElementById("folders-" + section);

    const professionalFoldersElement = document.getElementById(
      "folders-professional-info"
    );
    const professionalSectionArrowElement = document.getElementById(
      "section-arrow-professional-info"
    );
    const personalFoldersElement = document.getElementById(
      "folders-personal-info"
    );
    const personalSectionArrowElement = document.getElementById(
      "section-arrow-personal-info"
    );

    if (foldersElement) {
      foldersElement.classList.toggle("hidden");
    }
    if (
      foldersElement !== personalFoldersElement &&
      !personalFoldersElement.classList.contains("hidden")
    ) {
      personalFoldersElement.classList.toggle("hidden");
      personalSectionArrowElement.classList.toggle("rotate-90");
    }
    if (
      foldersElement !== professionalFoldersElement &&
      !professionalFoldersElement.classList.contains("hidden")
    ) {
      professionalFoldersElement.classList.toggle("hidden");
      professionalSectionArrowElement.classList.toggle("rotate-90");
    }

    const sectionArrowElement = document.getElementById(
      "section-arrow-" + section
    );
    if (sectionArrowElement) {
      sectionArrowElement.classList.toggle("rotate-90");
    }
  };

  const focusCurrentFolder = (folder) => {
    setFolder(folder);
    const currentSectionInfo = about.sections[currentSection].info;
    const currentSectionHasFolder = currentSectionInfo[folder];
    if (!currentSectionHasFolder) {
      const section = Object.keys(about.sections).find(
        (section) => about.sections[section].info[folder.title]
      );
      setCurrentSection(section);
    }
  };

  const toggleFiles = () => {
    const fileElement = document.getElementById("file-" + folder);
    if (fileElement) {
      fileElement.classList.toggle("hidden");
    }
  };

  const showContacts = () => {
    const contactsElement = document.getElementById("contacts");
    if (contactsElement) {
      contactsElement.classList.toggle("hidden");
    }

    const sectionArrowElement = document.getElementById("section-arrow");
    if (sectionArrowElement) {
      sectionArrowElement.classList.toggle("rotate-90");
    }
  };

  useEffect(() => {
    setLoading(false);
}, []);

  if (loading) {
    return (
      <div id="about-me" className="page" style={{ display: 'flex', justifyContent: 'center', alignItems: 'center' }}>
        <div className="text-menu-text">Loading...</div>
      </div>
    );
  }

  return (
    <div id="about-me" className="page">
      <div id="mobile-page-title">
        <h2>_about-me</h2>
      </div>

      <AboutMeSideMenu
        currentFolder={folder}
        currentSection={currentSection}
        focusCurrentSection={focusCurrentSection}
        focusCurrentFolder={focusCurrentFolder}
        showContacts={showContacts}
      />

      <div
        className={`flex flex-col lg:grid h-full w-full ${
          folder === "experience" ||
          folder === "education" ||
          folder === "bio" ||
          folder === "skills" 
            ? "lg:grid-cols-1"
            : "lg:grid-cols-2"
        }`}
      >
        {/* Your content goes here */}
        <LeftSideBox currentSection={currentSection} folder={folder} />

        {folder !== "experience" &&
        folder !== "education" &&
        folder !== "bio" &&
        folder !== "skills" ? (
          <RightSideBox />
        ) : (
          <></>
        )}
      </div>
    </div>
  );
};

export default AboutMe;
