"use client";
import {useEffect, useState} from 'react';
import './index.css';

import LeftSideBox from '@/components/AboutMe/LeftSideBox/LeftSideBox';
import RightSideBox from '@/components/AboutMe/RightSideBox/RightSideBox';
import AboutMeSideMenu from '@/components/AboutMeSideMenu/AboutMeSideMenu';
import { about, contact, gists } from './data';
type SectionKey = keyof typeof about.sections;
type FolderKey = string;

const AboutMe = () => {
  const [currentSection, setCurrentSection] = useState<SectionKey>('professional-info');
  const [folder, setFolder] = useState<FolderKey>('experience');
  const [loading, setLoading] = useState<boolean>(true);

  const focusCurrentSection = (section: SectionKey) => {
    setCurrentSection(about.sections[section].title as SectionKey);
    setFolder(Object.keys(about.sections[section].info)[0]);

    const foldersElement = document.getElementById("folders-" + section);
    const professionalFoldersElement = document.getElementById("folders-professional-info");
    const professionalSectionArrowElement = document.getElementById("section-arrow-professional-info");
    const personalFoldersElement = document.getElementById("folders-personal-info");
    const personalSectionArrowElement = document.getElementById("section-arrow-personal-info");

    if (foldersElement) {
      foldersElement.classList.toggle("hidden");
    }

    if (
        foldersElement !== personalFoldersElement &&
        personalFoldersElement &&
        !personalFoldersElement.classList.contains("hidden")
    ) {
      personalFoldersElement.classList.toggle("hidden");
      personalSectionArrowElement?.classList.toggle("rotate-90");
    }

    if (
        foldersElement !== professionalFoldersElement &&
        professionalFoldersElement &&
        !professionalFoldersElement.classList.contains("hidden")
    ) {
      professionalFoldersElement.classList.toggle("hidden");
      professionalSectionArrowElement?.classList.toggle("rotate-90");
    }

    const sectionArrowElement = document.getElementById("section-arrow-" + section);
    sectionArrowElement?.classList.toggle("rotate-90");
  };

  const focusCurrentFolder = (folderKey: FolderKey) => {
    setFolder(folderKey);
    const currentSectionInfo = about.sections[currentSection].info;
    const currentSectionHasFolder = currentSectionInfo[folderKey];

    if (!currentSectionHasFolder) {
      const section = Object.keys(about.sections).find((section) =>
          about.sections[section as SectionKey].info[folderKey]
      ) as SectionKey | undefined;

      if (section) {
        setCurrentSection(section);
      }
    }
  };

  const toggleFiles = () => {
    const fileElement = document.getElementById("file-" + folder);
    fileElement?.classList.toggle("hidden");
  };

  const showContacts = () => {
    const contactsElement = document.getElementById("contacts");
    contactsElement?.classList.toggle("hidden");

    const sectionArrowElement = document.getElementById("section-arrow");
    sectionArrowElement?.classList.toggle("rotate-90");
  };

  useEffect(() => {
    setLoading(false);
}, []);

  if (loading) {
    return (
      <div id="about-me" className="page" style={{ display: 'flex', justifyContent: 'center', alignItems: 'center' }}>
        <div className="text-menu-text">Loading...</div>
      </div>
    );
  }

  return (
    <div id="about-me" className="page">
      <div id="mobile-page-title">
        <h2>_about-me</h2>
      </div>

      <AboutMeSideMenu
        currentFolder={folder}
        currentSection={currentSection}
        focusCurrentSection={focusCurrentSection}
        focusCurrentFolder={focusCurrentFolder}
        showContacts={showContacts}
      />

      <div
        className={`flex flex-col lg:grid h-full w-full ${
          folder === "experience" ||
          folder === "education" ||
          folder === "bio" ||
          folder === "skills" 
            ? "lg:grid-cols-1"
            : "lg:grid-cols-2"
        }`}
      >
        {/* Your content goes here */}
        <LeftSideBox currentSection={currentSection} folder={folder} />

        {folder !== "experience" &&
        folder !== "education" &&
        folder !== "bio" &&
        folder !== "skills" ? (
          <RightSideBox />
        ) : (
          <></>
        )}
      </div>
    </div>
  );
};

export default AboutMe;
