
#sections {
  width: 5rem; /* 80px */
  height: 100%;
  border-right: 1px solid #1e2d3d;
}



/* @media (max-width: 768px) { */
@media (max-width: 1024px) {
  #about-me {
    display: flex;
    flex-direction: column;
    overflow: hidden;
    height: 100%;
    width: 100%;
  }

}


#about-me {
  display: flex;
  overflow: hidden;
  height: 100%;
  width: 100%;
}
/* effect test */
#about-me {
  opacity: 0; /* Initially hidden */
  animation: fadeIn .7s ease-in-out forwards; /* Animation with forward fill mode */
}

@keyframes fadeIn {
  to {
    opacity: 1; /* Fades in to full opacity */
  }
}

/* ------------------- */


#section-icon {
  @apply my-6 hover:cursor-pointer flex justify-center;
  opacity: 0.4;
}

#section-icon.active {
  opacity: 1;
}

#section-icon:hover {
  opacity: 1;
}

.tab-height {
  min-height: 35px;
  max-height: 35px;
}

#tab-mobile {
  padding: 25px 20px 0px 25px;
  align-items: flex-end;
}

#scroll-bar {
  width: 20px;
}

#scroll {
  width: 14px;
  height: 7px;
  background-color: #607b96;
}

#diple {
  @apply mx-3 w-2 max-w-fit;
}

.open {
  transform: rotate(90deg);
}

.active {
  color: white;
}

#right,
#left {
  height: 100%;
  overflow: hidden;
}

#gists-content {
  height: 100%;
  overflow: hidden;
}

@media (max-width: 1440px) {
  #gists-content {
    height: 100%;
    padding: 0px 25px;
    overflow: hidden;
  }

  #about {
    min-height: stretch;
  }
}

.section-arrow {
  transition: 0.1s;
}

#section-content #contacts {
  padding: 0px 25px;
}
