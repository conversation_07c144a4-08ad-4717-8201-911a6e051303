import ProfessionalInfoIcon from '/Assets/icons/info-professional.svg';
import PersonalInfoIcon from '/Assets/icons/info-personal.svg';

// types/about.ts

export type FileGroup = {
  [fileName: string]: string;
};

export type Folder = {
  title: string;
  description: string;
  files?: FileGroup;
};

export type Section = {
  title: string;
  icon: string;
  info: Record<string, Folder>;
};

export type AboutSections = {
  [key: string]: Section;
};

export type About = {
  sections: AboutSections;
};

// contact types
export type Contact = {
  direct: {
    title: string;
    sources: string[];
  };
  social: {
    [platform: string]: {
      title: string;
      url: string;
      user: string;
    };
  };
  find_me_also_in: {
    title: string;
    sources: {
      title: string;
      url: string;
      user: string;
    }[];
  };
};

export type Gists = Record<number, string>;


export const about = {
    "professional-info": {
  sections: {
      title: "professional-info",
      icon: ProfessionalInfoIcon,
      info: {
        experience: {
          title: "experience",
          description:
            "<br>Over the past 5 years, Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. <br><br> Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur.",
        },
        skills: {
          title: "skills",
          description:
            "<br>As a front-end developer, Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. <br><br> Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur.",
        },
      },
    },
    "personal-info": {
      title: "personal-info",
      icon: PersonalInfoIcon,
      info: {
        bio: {
          title: "bio",
          description:
            "<br> About me <br> I have 5 years of experience in web development lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. <br><br> Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat  nulla pariatur. Excepteur sint occaecat  officia deserunt mollit anim id est laborum.",
        },
        interests: {
          title: "interests",
          description:
            "<br>My main interest lies in learning and exploring new technologies to continuously enhance my knowledge. I have a strong desire to stay updated with the latest advancements in various fields, enabling me to adapt to a rapidly evolving digital landscape.",
        },
        education: {
          title: "education",
          description:
            "<br>I have always been passionate about lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. <br><br> Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur.",
          files: {
            "high-school": "I have been in 'Las viñas'...",
            university: "The university...",
          },
        },
      },
    },

  },
};

export const contact = {
  direct: {
    title: "contacts",
    sources: ["<EMAIL>", "+*************"],
  },
  social: {
    github: {
      title: "Github profile",
      url: "https://github.com/",
      user: "username",
    },
    facebook: {
      title: "Facebook profile",
      url: "https://facebook.com/",
      user: "username",
    },
    twitter: {
      title: "Twitter account",
      url: "https://twitter.com/",
      user: "username",
    },
  },
  find_me_also_in: {
    title: "find-me-also-in",
    sources: [
      {
        title: "YouTube channel",
        url: "https://www.youtube.com/",
        user: "username",
      },
      {
        title: "GuruShots profile",
        url: "https://gurushots.com/",
        user: "username",
      },
      {
        title: "Instagram account",
        url: "https://instagram.com/",
        user: "tanzim077",
      },
      {
        title: "Twitch profile",
        url: "https://twitch.com/",
        user: "username",
      },
    ],
  },
};

export const gists = {
  1: "a6712f4e7dade3d694b336ce77ad4f31",
  // 2: "83861a67e377633ee8368df01ee3a355",
  // 2: "694c1f32332788a2ac7f37b09e5aa40e",
};
