"use client";
import React, {useEffect, useState} from 'react';
import './index.css';

// Asset paths for icons
const arrow = '/Assets/icons/arrow.svg';
const link = '/Assets/icons/link.svg';
const close = '/Assets/icons/close.svg';

import Image from 'next/image';
import ContactForm from '@/components/ContactForm/ContactForm';
import FormContent from '@/components/FormContent/FormContent';


const contact = {
  direct: {
    title: "contacts",
    sources: ["<EMAIL>", "+*************"],
  },
  social: {
    github: {
      title: "Github profile",
      url: "https://github.com/",
      user: "username",
    },
    facebook: {
      title: "Facebook profile",
      url: "https://facebook.com/",
      user: "username",
    },
    twitter: {
      title: "Twitter account",
      url: "https://twitter.com/",
      user: "username",
    },
  },
  find_me_also_in: {
    title: "find-me-also-in",
    sources: [
      {
        title: "YouTube channel",
        url: "https://www.youtube.com/",
        user: "username",
      },
      {
        title: "GuruShots profile",
        url: "https://gurushots.com/",
        user: "username",
      },
      {
        title: "Instagram account",
        url: "https://instagram.com/",
        user: "username",
      },
      {
        title: "Twitch profile",
        url: "https://twitch.com/",
        user: "username",
      },
    ],
  },
};

const Contact: React.FC = () => {
  const [name, setName] = useState("");
  const [email, setEmail] = useState("");
  const [title, setTitle] = useState("");
  const [message, setMessage] = useState("");
  const [socialMediaData, setSocialMediaData] = useState([]);
  const [contactData, setContactData] = useState([]);
  const [configData, setConfigData] = useState([]);
  const [loading, setLoading] = useState(true);

  // Fetch both social media and contact data
  useEffect(() => {
    const fetchData = async () => {
      try {
        const baseUrl = process.env.NEXT_PUBLIC_SERVER_URL || 'http://localhost:8080/api';

        // Fetch social media data
        const socialMediaResponse = await fetch(`/api/social-media`);
        if (socialMediaResponse.ok) {
          const socialData = await socialMediaResponse.json();
          setSocialMediaData(socialData);
        }

        // Fetch config data
        const configResponse = await fetch(`/api/config`);
        if (configResponse.ok) {
          const configData = await configResponse.json();
          setConfigData(configData);
        }

        // Fetch contact data
        const contactResponse = await fetch(`/api/contact`);
        if (contactResponse.ok) {
          const contactDataResult = await contactResponse.json();
          console.log('Contact data fetched:', contactDataResult);
          setContactData(contactDataResult);
        } else {
          console.log('Contact API response not ok:', contactResponse.status);
        }

      } catch (error) {
        console.error('Error fetching data:', error);
        // Use fallback data
        setSocialMediaData([
          { name: 'GitHub', link: 'https://github.com/tanzim077', active: true },
          { name: 'LinkedIn', link: 'https://linkedin.com/in/tanzim077', active: true },
          { name: 'Twitter', link: 'https://twitter.com/tanzim077', active: true }
        ]);
        setContactData([]);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  const open = (elementId) => {
    const element = document.getElementById(elementId);
    const arrow = element.querySelector(".arrow");
    const links = element.querySelector("#links");

    if (links.style.display === "block") {
      links.style.display = "none";
      arrow.style.transform = "rotate(0deg)";
    } else {
      links.style.display = "block";
      arrow.style.transform = "rotate(90deg)";
    }
  };

  // Get dynamic contact data
  const getContactSources: React.FC = () => {
    console.log('Contact data available:', contactData);

    if (contactData.length === 0) {
      // Fallback to static data if no dynamic data
      console.log('Using fallback contact data');
      return ["", ""];
    }

    // Extract email and phone from contact data
    const sources = [];

    // Look for email contact
    const emailContact = contactData.find(contact =>
      contact.active &&
      (contact.type?.toLowerCase().includes('email') ||
       contact.name?.toLowerCase().includes('email') ||
       contact.link?.includes('@'))
    );
    if (emailContact) {
      sources.push(emailContact.link);
    }

    // Look for phone contact
    const phoneContact = contactData.find(contact =>
      contact.active &&
      (contact.type?.toLowerCase().includes('phone') ||
       contact.name?.toLowerCase().includes('phone') ||
       contact.link?.includes('+') ||
       /^\+?\d+/.test(contact.link))
    );
    if (phoneContact) {
      sources.push(phoneContact.link);
    }

    // If no email/phone found in contact data, use fallback
    if (sources.length === 0) {
      return ["", ""];
    }

    return sources;
  };

  // Get social media data
  const getSocialMediaSources: React.FC = () => {
    if (socialMediaData.length === 0) {
      // Fallback to static data
      return [
        { title: "GitHub", url: "https://github.com/tanzim077" },
        { title: "LinkedIn", url: "https://linkedin.com/in/tanzim077" },
        { title: "Twitter", url: "https://twitter.com/tanzim077" },
        { title: "YouTube", url: "https://youtube.com/@tanzim077" },
      ];
    }

    return socialMediaData
      .filter(social => social.active)
      .map(social => ({
        title: social.name,
        url: social.link,
      }));
  };

  // Skeleton loading component for sidebar
  const renderSidebarSkeleton = () => (
    <div
      id="page-menu"
      className="w-full h-full flex flex-col border-right"
    >
      <div id="contacts" className="submenu">
        <div className="title">
          <Image src={arrow} className="arrow" alt="Arrow"></Image>
          <h3> contacts </h3>
        </div>
        <div id="links">
          {[1, 2].map((index) => (
            <div className="link" key={index}>
              <Image src={link} alt="Icon"></Image>
              <div className="skeleton-text" style={{width: '180px', height: '14px'}}></div>
            </div>
          ))}
        </div>
      </div>

      <div id="find-me-in" className="submenu border-top">
        <div className="title">
          <Image src={arrow} className="arrow" alt="Arrow"></Image>
          <h3> find-me-also-in </h3>
        </div>
        <div id="links">
          {[1, 2, 3, 4].map((index) => (
            <div className="link" key={index}>
              <Image src={link} alt="Link"></Image>
              <div className="skeleton-text" style={{width: `${120 + (index * 20)}px`, height: '14px'}}></div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );

  return (
    <>
      <div id="contact-me" className="page">

        <div id="mobile-page-title">
          <h2>_contact-me</h2>
        </div>

        {loading ? renderSidebarSkeleton() : (
          <div
            id="page-menu"
            className="w-full h-full flex flex-col border-right"
          >
            <div id="contacts" className="submenu">
              <div className="title" onClick={() => open("contacts")}>
                <Image src={arrow} className="arrow" alt="Arrow"></Image>
                <h3> contacts </h3>
              </div>
              <div id="links">
                {getContactSources().map((source, index) => (
                  <div className="link" key={index}>
                    <Image src={link} alt="Icon"></Image>
                    <a
                      href={source.includes('@') ? `mailto:${source}` : `tel:${source}`}
                      className="font-fira_retina text-menu-text hover:text-white"
                    >
                      {source}
                    </a>
                  </div>
                ))}
              </div>
            </div>

            <div id="find-me-in" className="submenu border-top">
              <div className="title" onClick={() => open("find-me-in")}>
                <Image src={arrow} className="arrow" alt="Arrow"></Image>
                <h3> find-me-also-in </h3>
              </div>
              <div id="links">
                {getSocialMediaSources().map((source, index) => (
                  <div className="link" key={index}>
                    <Image src={link} alt="Link"></Image>
                    <a
                      href={source.url}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="font-fira_retina text-menu-text hover:text-white"
                    >
                      {source.title}
                    </a>
                  </div>
                ))}
              </div>
            </div>
          </div>
        )}

        {/* Skeleton loading for main content */}
        {loading ? (
          <div className="flex flex-col w-full">
            <div className="tab-height w-full hidden lg:flex border-right border-bot items-center">
              <div className="flex items-center border-right h-full">
                <p className="font-fira_regular text-menu-text text-sm px-3">
                  contacts
                </p>
                <Image src={close} alt="close-icon" className="m-3"></Image>
              </div>
            </div>
            <div className="flex lg:grid lg:grid-cols-2 h-full w-full">
              <div
                id="left"
                className="h-full w-full flex flex-col border-right items-center p-6"
              >
                {/* Contact form skeleton */}
                <div className="w-full max-w-md space-y-4">
                  <div className="skeleton-text" style={{width: '100%', height: '16px', marginBottom: '8px'}}></div>
                  <div className="skeleton-text" style={{width: '100%', height: '40px', marginBottom: '16px'}}></div>

                  <div className="skeleton-text" style={{width: '100%', height: '16px', marginBottom: '8px'}}></div>
                  <div className="skeleton-text" style={{width: '100%', height: '40px', marginBottom: '16px'}}></div>

                  <div className="skeleton-text" style={{width: '100%', height: '16px', marginBottom: '8px'}}></div>
                  <div className="skeleton-text" style={{width: '100%', height: '120px', marginBottom: '24px'}}></div>

                  <div className="skeleton-text" style={{width: '120px', height: '40px'}}></div>
                </div>
              </div>
              <div id="right" className="h-full w-full hidden lg:flex">
                <div className="form-content p-6 w-full">
                  {/* Form preview skeleton */}
                  <div className="space-y-3">
                    <div className="skeleton-text" style={{width: '60px', height: '14px'}}></div>
                    <div className="skeleton-text" style={{width: '200px', height: '16px'}}></div>
                    <div className="skeleton-text" style={{width: '180px', height: '16px'}}></div>
                    <div className="skeleton-text" style={{width: '220px', height: '16px'}}></div>
                    <div className="skeleton-text" style={{width: '160px', height: '16px'}}></div>
                    <div className="skeleton-text" style={{width: '240px', height: '16px'}}></div>
                    <div className="skeleton-text" style={{width: '190px', height: '16px'}}></div>
                    <div className="skeleton-text" style={{width: '210px', height: '16px'}}></div>
                  </div>
                </div>
                <div
                  id="scroll-bar"
                  className="h-full border-left flex justify-center py-1"
                >
                  <div id="scroll"></div>
                </div>
              </div>
            </div>
          </div>
        ) : (
          <div className="flex flex-col w-full">
            <div className="tab-height w-full hidden lg:flex border-right border-bot items-center">
              <div className="flex items-center border-right h-full">
                <p className="font-fira_regular text-menu-text text-sm px-3">
                  contacts
                </p>
                <Image src={close} alt="close-icon" className="m-3"></Image>
              </div>
            </div>
            <div className="flex lg:grid lg:grid-cols-2 h-full w-full">
              <div
                id="left"
                className="h-full w-full flex flex-col border-right items-center"
              >
                <ContactForm
                  name={name}
                  email={email}
                  title={title}
                  message={message}
                  setName={setName}
                  setEmail={setEmail}
                  setTitle={setTitle}
                  configData={configData}
                  setMessage={setMessage}
                ></ContactForm>
              </div>
              <div id="right" className="h-full w-full hidden lg:flex">
                <div className="form-content">
                  <FormContent
                    name={name}
                    email={email}
                    message={message}
                  ></FormContent>
                </div>
                <div
                  id="scroll-bar"
                  className="h-full border-left flex justify-center py-1"
                >
                  <div id="scroll"></div>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </>
  );
};

export default Contact;
