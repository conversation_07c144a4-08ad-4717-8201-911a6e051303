"use client";

import ProjectCard from '@/components/shared/ProjectCard/ProjectCard';
import React from 'react';

const data = [
  {
    title: "World_of_Cactus",
    description:
      "sad town selection cream yet somehow bottle among dark date tobacco join hay quickly rubbed would shelf teacher population lamp winter guard bound journey",
    link: "https://github.com/tanzim077",
  },
  {
    title: "chat_Mania",
    description:
      "sad town selection cream yet somehow bottle among dark date tobacco join hay quickly rubbed would shelf teacher population lamp winter guard bound journey",
    link: "https://github.com/tanzim077",
  },
  {
    title: "explore_the_Nature",
    description:
      "sad town selection cream yet somehow bottle among dark date tobacco join hay quickly rubbed would shelf teacher population lamp winter guard bound journey",
    link: "https://github.com/tanzim077",
  },
  {
    title: "weather_App",
    description:
      "sad town selection cream yet somehow bottle among dark date tobacco join hay quickly rubbed would shelf teacher population lamp winter guard bound journey",
    link: "https://github.com/tanzim077",
  },
];

const Page: React.FC = () => {
  return (
    <div className="grid grid-cols-3 gap-4 m-2">
      {data.map((item, index) => (
        <ProjectCard key={item.title} item={item} index={index} />
      ))}
    </div>
  );
};

export default Page;
