
import React from 'react';

const head: React.FC = () => {
  return (
    <>
      <title>Who is <PERSON><PERSON><PERSON></title>
      <meta charSet="utf-8" />
      <meta name="viewport" content="width=device-width, initial-scale=1" />
      <meta name="description" content="<PERSON><PERSON><PERSON>'s Portfolio" />
      <meta name="author" content="<PERSON><PERSON><PERSON>" />
      <link rel="icon" href="/favicon.ico" />
      <link rel="preconnect" href="https://fonts.googleapis.com" />
      <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
      <link
        href="https://fonts.googleapis.com/css2?family=Fira+Code&display=swap"
        rel="stylesheet"
      />
    </>
  );
};

export default head;
