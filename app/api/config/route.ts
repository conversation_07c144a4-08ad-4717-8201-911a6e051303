import axios from 'axios';
import { NextRequest } from 'next/server';

const baseUrl = `${process.env.NEXT_PUBLIC_SERVER_URL}/v1/config/data`

export async function GET(request: NextRequest) {
    try {

        const origin = new URL(request.url).origin;

        const res = await axios.get(baseUrl, {
            headers: {
                referer: origin
            }
        });
        const data = res.data;
        return new Response(JSON.stringify(data), {
            status: res.status,
            headers: {
                'Content-Type': 'application/json'
            }
                });
    } catch (error) {
        console.log(error);
        return new Response(JSON.stringify({ error: 'Internal Server Error' }), {
            status: 500,
            headers: {
                'Content-Type': 'application/json'
            }
        });
    }
}

