

/* effect test */
#project-menu {
  opacity: 0; /* Initially hidden */
  animation: fadeIn .7s ease-in-out forwards; /* Animation with forward fill mode */
}

@keyframes fadeIn {
  to {
    opacity: 1; /* Fades in to full opacity */
  }
}

#filters {
  padding: 10px 25px;
}

#tab {
  padding: 25px 25px 5px;
  flex-wrap: wrap;
}

/* Skill filter styling */
.tech-icon {
  opacity: 0.4;
  transition: opacity 0.2s ease;
}

input[type="checkbox"]:checked + label .tech-icon {
  opacity: 1;
}

input[type="checkbox"]:checked + label .skill-name {
  color: white;
}

.skill-name {
  color: #607B96;
  transition: color 0.2s ease;
}

label:hover .tech-icon {
  opacity: 0.8;
}

label:hover .skill-name {
  color: rgba(255, 255, 255, 0.8);
}


#project {
  min-width: 300px;
  margin-bottom: 15px;
  padding-right: 15px;
}

#project-card {
  border: 1px solid #1E2D3D;
  background-color: #011221;
  border-radius: 15px;
  width: 100%;
}

#showcase {
  border-top-right-radius: 15px;
  border-top-left-radius: 15px;
  object-position: center;
}

#view-button {
  background-color: #1C2B3A;
}

#view-button:hover {
  background-color: #263B50;
}

#window {
  max-height: 120px;
  position: relative;
  overflow: hidden;
}

input[type="checkbox"] {
  appearance: none;
  background-color: transparent;
  width: 1.15em;
  height: 1.15em;
  border: 2px solid currentColor;
  border-radius: 0.15em;
  margin-top: 1px;
}

input[type="checkbox"]:checked {
  background-color: currentColor;
  background-image: url("data:image/svg+xml;utf8,<svg width='13' height='10' viewBox='0 0 13 10' fill='none' xmlns='http://www.w3.org/2000/svg'><path d='M5.38587 7.2802L11.9718 0.693573L12.9856 1.70668L5.38587 9.30641L0.826172 4.74671L1.83928 3.73361L5.38587 7.2802Z' fill='white'/></svg>");
  background-repeat: no-repeat;
  background-position: center;
}

input[type="checkbox"]:checked:hover {
  box-shadow: #607b968b 0px 0px 0px 2px;
}

input[type="checkbox"]:not(:checked) {
  border-color: currentColor;
}

input[type="checkbox"]:hover {
  cursor: pointer;
  background-color: currentColor;
  background-image: url("data:image/svg+xml;utf8,<svg width='13' height='10' viewBox='0 0 13 10' fill='none' xmlns='http://www.w3.org/2000/svg'><path d='M5.38587 7.2802L11.9718 0.693573L12.9856 1.70668L5.38587 9.30641L0.826172 4.74671L1.83928 3.73361L5.38587 7.2802Z' fill='white'/></svg>");
  background-repeat: no-repeat;
  background-position: center;
  box-shadow: #607b968b 0px 0px 0px 2px;
}

input[type="checkbox"]:hover:not(:checked) {
  cursor: pointer;
  background-color: rgba(0, 0, 0, 0.1);
  background-image: none;
  box-shadow: #607b968b 0px 0px 0px 2px;
}

input[type="checkbox"]:focus {
  box-shadow: none;
}

@media (max-width: 768px) {
  #filters {
    padding: 10px 20px;
    display: grid;
    grid-template-columns: repeat(2, minmax(0, 1fr));
    gap: 8px;
    max-height: 500px;
    overflow-y: auto;
    transition: max-height 0.3s ease;
  }

  #filters.filters-collapsed {
    max-height: 0;
    overflow: hidden;
    padding-top: 0;
    padding-bottom: 0;
  }

  #filter-menu {
    border-bottom: 1px solid #1E2D3D;
    margin-bottom: 15px;
  }

  #section-content-title {
    padding: 10px 20px;
    border-bottom: 1px solid #1E2D3D;
  }

  #projects-case {
    padding: 0px 25px 40px;
    justify-items: start;
  }

  #project {
    min-width: 100%;
    padding-right: 0;
  }
}

@media (min-width: 768px) {
  #projects-case {
    grid-template-columns: repeat(2, minmax(0, 1fr));
    padding: 20px 20px;
    justify-items: start;
    column-gap: 20px;
  }
  #project {
    width: 100%;
    min-width: 100%;
    padding-right: 0;
  }
}



@media (min-width: 1350px) {
  #projects-case {
    grid-template-columns: repeat(3, minmax(0, 1fr));
    min-height: 78vh;
    padding: 20px 20px;
    justify-items: start;
    column-gap: 20px;
  }

  #project {
    width: 100%;
    min-width: 100%;
    padding-right: 0;
  }
}

@keyframes animateToBottom {
  from {
    transform: translate3d(0, -200px, 0);
  }
  to {
    transform: translate3d(0, 10px, 0);
  }
}


.project-box{
  height: 100%;
  width: 100%;
  transition: all 0.3s ease-in-out;
  position: relative;
  overflow: scroll;


}

.tab-height {
  min-height: 35px;
  max-height: 35px;
}

.project-items{
  width: 100%;
  height: 100%;
  overflow: scroll;


}