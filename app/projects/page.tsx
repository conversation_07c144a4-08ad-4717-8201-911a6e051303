
'use client';
import Image from 'next/image';
import ImageSlideshow from '@/components/shared/ImageSlideshow/ImageSlideshow';
import '@/components/shared/ImageSlideshow/index.css';
import { useEffect, useState } from 'react';
const project6 = '/Assets/images/projects/ethereum.png';
const project5 = '/Assets/images/projects/tetris-game.png';
const project4 = '/Assets/images/projects/ui-animations.png';
const project1 = '/Assets/images/projects/ui-animations2.png';
const project3 = '/Assets/images/projects/worldmap.png';
import './index.css';
const arrow = '/Assets/icons/arrow.svg';
const close = '/Assets/icons/close.svg';

// const CSS = '/Assets/icons/techs/css.svg';
// const HTML = '/Assets/icons/techs/html.svg';
// const ReactIcon = '/Assets/icons/techs/react.svg';
const CSS = '/Assets/images/icons/CSS3.svg';
const HTML = '/Assets/images/icons/Html5.svg';
const MongoDB = '/Assets/images/icons/MongoDB.svg';
const NodeJs = '/Assets/images/icons/NodeJs.svg';
const PostgreSQL = '/Assets/images/icons/PostgreSQL.svg';
const Python = '/Assets/images/icons/Python.svg';
const ReactIcon = '/Assets/images/icons/React.svg';
const TypeScript = '/Assets/images/icons/TypeScript.svg';
const ExpressJs = '/Assets/images/icons/ex.png';
const Nest = '/Assets/images/icons/nest.svg';
const Next = '/Assets/images/icons/next.svg';

const API_BASE = process.env.NEXT_PUBLIC_ADMIN_API

const data = {
    projects: [
        {
            title: '_explore_the_nature_server_side',
            description: 'Express.js Backend for a travel booking web application.',
            img: project1,
            tech: ['Express', 'Node', 'MongoDB'],
            url: 'https://github.com/tanzim077/explore-the-nature-server-side',
        },
        {
            title: '_cactus_world_server_side',
            description: 'Express.js Backend for a cactus selling web application.',
            img: project4,
            tech: ['Express', 'Node', 'MongoDB'],
            url: 'https://github.com/tanzim077/Cactus_world_server_side',
        },
        {
            title: '_explore_the_nature_server_side',
            description: 'Simple react application to explore the nature',
            img: project6,
            tech: ['React', 'HTML', 'CSS'],
            url: 'https://github.com/tanzim077/explore-the-nature-client-side',
        },
        {
            title: '_news_board_server_side_graphql',
            description: 'Simple graphql server for a news board application',
            img: project5,
            tech: ['GraphQL', 'Express', 'Node', 'MongoDB', 'SQL'],
            url: 'https://github.com/tanzim077/news-board-graphql/tree/main/server',
        },
        {
            title: '_news_board_client_side_graphql',
            description: 'Simple graphql server for a news board application',
            img: project3,
            tech: ['GraphQL', 'React', 'HTML', 'CSS'],
            url: 'https://github.com/tanzim077/news-board-graphql/tree/main/client',
        },
    ],
};

import { defaultImage } from '@/utils/constants';

// Type definitions
interface Project {
    id?: string;
    _id?: string;
    name: string;
    projectDescription: string;
    coverImages?: string[];
    skill?: Skill[];
    tech?: string[];
    projectLiveLink: string;
}

interface Skill {
    id: string;
    _id?: string;
    name: string;
    logo?: string;
}

const icons = [ReactIcon, HTML, CSS, Next, Nest, ExpressJs, TypeScript, NodeJs, PostgreSQL, MongoDB, Python];

const Projects: React.FC = () => {
    const [techs, setTechs] = useState<string[]>([
        'React',
        'HTML',
        'CSS',
        'Next',
        'Nest',
        'Express',
        'TypeScript',
        'Node.JS',
        'PostgreSQL',
        'MongoDB',
        'Python',
    ]);

    const techIcons: Record<string, any> = {
        React: ReactIcon,
        HTML: HTML,
        CSS: CSS,
        Next: Next,
        Nest: Nest,
        Express: ExpressJs,
        TypeScript: TypeScript,
        Node: NodeJs,
        PostgreSQL: PostgreSQL,
        MongoDB: MongoDB,
        Python: Python,
    };
    const [filters, setFilters] = useState<string[]>(['all']);
    // Initialize with empty array to avoid hydration mismatch
    const [projects, setProjects] = useState<Project[]>([]);

    // Initialize state
    const [loading, setLoading] = useState<boolean>(true);
    const [skillsData, setSkillsData] = useState<Skill[]>([]);

    // Fetch skills data
    useEffect(() => {
        // Fetch skills
        fetch(`/api/skill`)
            .then((res) => res.json())
            .then((data) => {
                data?.statusCode === 404 ? setSkillsData([]) : setSkillsData(data);
            })
            .catch(() => setSkillsData([]));
    }, []);

    const filterProjects = (skill: Skill) => {
        // Use the skill ID as a reliable identifier
        const skillId = skill.id;
        const check = document.getElementById(`skill-checkbox-${skillId}`) as HTMLInputElement;

        if (check && check.checked) {
            setFilters((prevFilters) => {
                if (prevFilters.includes('all')) {
                    return [skillId];
                } else {
                    return [...prevFilters, skillId];
                }
            });
        } else {
            setFilters((prevFilters) => {
                const newFilters = prevFilters.filter((item) => item !== skillId);
                if (newFilters.length === 0) {
                    return ['all'];
                } else {
                    return newFilters;
                }
            });
        }
    };

    const hideSection = () => {
        // On mobile, we'll toggle a class to collapse/expand the filters section
        const filterMenu = document.getElementById('filters');
        if (filterMenu) {
            filterMenu.classList.toggle('filters-collapsed');
        }
        const sectionArrow = document.getElementsByClassName('section-arrow')[0] as HTMLElement;
        if (sectionArrow) {
            sectionArrow.classList.toggle('rotate-90');
        }
    };

    const filterProjectsBy = (filters: string[], projectsToFilter: Project[]): Project[] => {
        if (!projectsToFilter || projectsToFilter.length === 0) {
            return [];
        }

        if (filters[0] === 'all') {
            return projectsToFilter;
        } else {
            return projectsToFilter.filter((project) => {
                // Check if project has skills and if any of them match the selected filters
                const skills = project.skill || project.tech || [];
                return Array.isArray(skills) && skills.length > 0 && skills.some(skillObj => {
                    // Handle both object skills and string skills
                    if (typeof skillObj === 'string') {
                        return filters.includes(skillObj);
                    } else if (skillObj && typeof skillObj === 'object') {
                        return filters.includes(skillObj._id || skillObj.id || '');
                    }
                    return false;
                });
            });
        }
    };

    // Store the original projects data when it's fetched
    const [allProjects, setAllProjects] = useState<Project[]>([]);

    // Fetch projects and store them
    useEffect(() => {
        fetch(`/api/project`)
            .then((res) => res.json())
            .then((data) => {
                if (data?.statusCode === 404) {
                    setProjects([]);
                    setAllProjects([]);
                } else {
                    setProjects(data);
                    setAllProjects(data); // Store the original data
                }
                setLoading(false);
            })
            .catch(() => {
                setProjects([]);
                setAllProjects([]);
                setLoading(false);
            });
    }, []);

    // Apply filters whenever filters change
    useEffect(() => {
        const updatedProjects = filterProjectsBy(filters, allProjects);
        setProjects(updatedProjects);
    }, [filters, allProjects]);

    return (
        <div id="project-menu" className="project-box">
            {loading ? (
                <div className="flex justify-center items-center h-full w-full">
                    <p className="text-white">Loading projects...</p>
                </div>
            ) : (
                <div className="flex flex-col flex-auto lg:flex-row overflow-hidden">
                    <div id="mobile-page-title">
                        <h2>_projects</h2>
                    </div>

                    {/* Mobile section title removed to avoid duplication */}

                    <div
                        id="filter-menu"
                        className=" w-full border-right font-fira_regular text-menu-text flex flex-col"
                    >
                        <div id="section-content-title" className="flex items-center min-w-full cursor-pointer"
                             onClick={hideSection}>
                            <Image
                                id="section-arrow-menu"
                                src={arrow}
                                alt=""
                                width={10}
                                height={10}
                                className="section-arrow mx-3 open"
                            ></Image>
                            <p className="font-fira_regular text-white text-sm">projects</p>
                        </div>

                        <nav id="filters" className="w-full flex-col">
                            {skillsData.map((skill, index) => (
                                <div className="flex items-center py-2" key={skill.id || index}>
                                    <input
                                        type="checkbox"
                                        id={`skill-checkbox-${skill.id}`}
                                        checked={filters.includes(skill.id)}
                                        onChange={() => filterProjects(skill)}
                                    />
                                    <label
                                        htmlFor={`skill-checkbox-${skill.id}`}
                                        className="flex items-center cursor-pointer"
                                    >
                                        <Image
                                            src={skill.logo || defaultImage}
                                            width={10}
                                            height={10}
                                            alt={skill.id || 'tech-icon'}
                                            className="tech-icon w-5 h-5 mx-4"
                                        />
                                        <span className="skill-name">{skill.name}</span>
                                    </label>
                                </div>
                            ))}
                        </nav>
                    </div>

                    <div className="  flex flex-col w-full overflow-hidden">
                        <div className="tab-height w-full hidden lg:flex border-bot items-center">
                            <div className="flex items-center border-right h-full">
                                {filters[0] === 'all' ? (
                                    <p className="font-fira_regular text-menu-text text-sm px-3">all;</p>
                                ) : (
                                    <>
                                        {filters.map((filterId) => {
                                            // Find the skill object that matches this ID
                                            const skill = skillsData.find(s => s.id === filterId);
                                            return skill ? (
                                                <div className="flex items-center px-3" key={filterId}>
                                                    <p className="font-fira_regular text-white text-sm mr-1">{skill.name};</p>
                                                    <button
                                                        onClick={() => {
                                                            setFilters(prev => prev.filter(id => id !== filterId));
                                                        }}
                                                        className="ml-1 text-menu-text hover:text-white"
                                                    >
                                                        <Image src={close} width={10} height={10} alt="remove filter"/>
                                                    </button>
                                                </div>
                                            ) : null;
                                        })}
                                    </>
                                )}
                                {filters[0] !== 'all' && (
                                    <button
                                        onClick={() => setFilters(['all'])}
                                        className="mx-3 text-menu-text hover:text-white"
                                    >
                                        <Image src={close} width={14} height={14} alt="clear all filters"/>
                                    </button>
                                )}
                            </div>
                        </div>
                        <div id="tab" className="flex lg:hidden items-center">
                            <span className="text-white"> {'//'}</span>
                            <p className="font-fira_regular text-white text-sm px-3">projects</p>
                            <span className="text-menu-text"> / </span>
                            {filters[0] === 'all' ? (
                                <p className="font-fira_regular text-menu-text text-sm px-3">all;</p>
                            ) : (
                                <>
                                    {filters.map((filterId) => {
                                        // Find the skill object that matches this ID
                                        const skill = skillsData.find(s => s.id === filterId);
                                        return skill ? (
                                            <p className="font-fira_regular text-white text-sm px-3" key={filterId}>
                                                {skill.name};
                                            </p>
                                        ) : null;
                                    })}
                                </>
                            )}
                        </div>
                        <div
                            id="projects-case"
                            className="grid grid-cols-1 lg:grid-cols-2 max-w-full h-full overflow-auto px-4"
                        >
                            {projects.length === 0 && (
                                <div
                                    id="not-found"
                                    className="flex flex-col font-fira_retina text-menu-text my-5 h-full justify-center items-center col-span-full"
                                >
                                    <span className="flex justify-center text-4xl pb-3">X__X</span>
                                    <span className="text-white flex justify-center text-xl">No matching projects</span>
                                    <span className="flex justify-center">for these technologies</span>
                                    <button
                                        onClick={() => setFilters(['all'])}
                                        className="mt-4 py-2 px-4 bg-[#1C2B3A] text-white rounded hover:bg-[#263B50] transition-colors"
                                    >
                                        Reset Filters
                                    </button>
                                </div>
                            )}
                            {projects.map((project, index) => (
                                <div id="project" className="" key={index}>
                  <span className="flex text-sm my-3">
                    {index == null && <h3 className="text-purplefy font-fira_bold mr-3">Project {key + 1} </h3>}
                      <h4 className="font-fira_retina text-menu-text">{`// ${project.name}`}</h4>
                  </span>
                                    <div id="project-card" className="flex flex-col">
                                        <div id="window">
                                            <ImageSlideshow
                                                images={project.coverImages?.length ? project.coverImages : []}
                                                interval={5000}
                                                skills={project.skill || []}
                                            />
                                        </div>
                                        <div className="pb-8 pt-6 px-6 border-top">
                                            <p className="text-menu-text font-fira_retina text-sm mb-5">{project.projectDescription}</p>
                                            <a
                                                id="view-button"
                                                href={project.projectLiveLink}
                                                target="_blank"
                                                className="text-white font-fira_retina py-2 px-4 w-fit text-xs rounded-lg"
                                            >
                                                {' '}
                                                view-project{' '}
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            ))}
                        </div>
                    </div>
                </div>
            )}
        </div>
    );
};

export default Projects;
