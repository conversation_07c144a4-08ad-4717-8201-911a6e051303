'use client';
import {useEffect, useState} from 'react';
// Define types for the expected structure
type SocialMedia = {
    name?: string;
    platform?: string;
    url?: string;
    link?: string;
};

type User = {
    name?: string;
    profileImage?: string;
};

type Company = {
    name?: string;
    designation?: string;
};

type Config = {
    githubURL?: string;
    image?: string;
};

type CommonData = {
    socialMedias?: SocialMedia[];
    user?: User;
    company?: Company;
    totalExperience?: string;
    config?: Config;
};

export const useCommonData = () => {
    const [data, setData] = useState<CommonData | null>(null);
    const [loading, setLoading] = useState<boolean>(true);
    const [error, setError] = useState<string | null>(null);
    const baseUrl = process.env.NEXT_PUBLIC_SERVER_URL ;

    useEffect(() => {
        const fetchCommonData = async () => {
            try {
                setLoading(true);
                setError(null);

                const baseUrl = process.env.NEXT_PUBLIC_SERVER_URL ;
                const fullUrl = "/api/user";
                const response = await fetch(fullUrl);

                if (!response.ok) {
                    const errorText = await response.text();
                    throw new Error(`Failed to fetch common data: ${response.status} - ${errorText}`);
                }

                const result = await response.json();

                // The backend returns the data directly
                setData(result);
            } catch (err:any) {
                console.error('Error fetching common data:', err);
                setError(err.message);
            } finally {
                setLoading(false);
            }
        };

        fetchCommonData();
    }, []);

    // Helper function to get GitHub link from social medias
    const getGithubLink = () => {
        if (!data?.socialMedias) return 'https://github.com/tanzim077';

        const github = data.socialMedias.find(
            social => social.name?.toLowerCase().includes('github') ||
                social.platform?.toLowerCase().includes('github')
        );

        return github?.url || github?.link || 'https://github.com/tanzim077';
    };

    // Helper function to format user name
    const getUserName = () => {
        return data?.user?.name || 'Tanzim Ahmed';
    };

    // Helper function to get current company info
    const getCurrentCompany = () => {
        return data?.company?.name || 'N/A';
    };

    // Helper function to get current designation
    const getCurrentDesignation = () => {
        return data?.company?.designation || 'N/A';
    };

    // Helper function to get total experience
    const getTotalExperience = () => {
        return data?.totalExperience || 'N/A';
    };

    // Helper function to get user profile image
    const getUserProfileImage = () => {
        return data?.user?.profileImage || null;
    };

    // Helper function to get GitHub link from config (preferred) or social medias (fallback)
    const getConfigGithubLink = () => {
        // First try to get from config.githubURL
        if (data?.config?.githubURL) return data.config.githubURL;

        // Fallback to existing social media logic
        return getGithubLink();
    };

    // Helper function to get configuration image
    const getConfigImage = () => {
        return data?.config?.image || null;
    };

    // Helper function to get GitHub username from config URL
    const getGithubUsername = () => {
        const githubUrl = data?.config?.githubURL;
        if (githubUrl) {
            // Extract username from GitHub URL (e.g., https://github.com/tanzim077 -> tanzim077)
            const match = githubUrl.match(/github\.com\/([^\/]+)/);
            return match ? match[1] : '';
        }
        return '';
    };

    // Helper function to get Twitter username from social media
    const getTwitterUsername = () => {
        if (!data?.socialMedias) return '';
        const twitter = data.socialMedias.find(
            social => social.name?.toLowerCase().includes('twitter') ||
                social.platform?.toLowerCase().includes('twitter')
        );

        if (twitter?.link) {
            // Extract username from Twitter URL (e.g., https://twitter.com/tanzim077 -> tanzim077)
            const twitterMatch = twitter.link.match(/twitter\.com\/([^\/]+)/);
            const xMatch = twitter.link.match(/x\.com\/([^\/]+)/);
            const match = twitterMatch || xMatch;
            return match ? match[1] : '';
        }

        return '';
    };

    // Helper function to get Facebook username from social media
    const getFacebookUsername = () => {
        if (!data?.socialMedias) return '';

        const facebook = data.socialMedias.find(
            social => social.name?.toLowerCase().includes('facebook') ||
                social.platform?.toLowerCase().includes('facebook')
        );

        if (facebook?.link) {
            // Extract username from Facebook URL (e.g., https://facebook.com/tanzim077 -> tanzim077)
            const match = facebook.link.match(/facebook\.com\/([^\/]+)/);
            return match ? match[1] : '';
        }

        return '';
    };

    return {
        data,
        loading,
        error,
        // Helper functions
        getGithubLink,
        getUserName,
        getCurrentCompany,
        getCurrentDesignation,
        getTotalExperience,
        getUserProfileImage,
        // New helper functions for config data
        getConfigGithubLink,
        getConfigImage,
        // Social media username helpers
        getGithubUsername,
        getTwitterUsername,
        getFacebookUsername,
        // Retry function
        refetch: () => {
            setLoading(true);
            setError(null);
            // Re-trigger the effect by updating a dependency
            window.location.reload();
        }
    };
};



