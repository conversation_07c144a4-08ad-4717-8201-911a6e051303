"use client";
import React from 'react';
import hljs from 'highlight.js';
import Image from 'next/image';
import {useCallback, useEffect, useState} from 'react';
import type { GistData } from '@/types';
const close = '/Assets/icons/close.svg';
const commentIcon = '/Assets/icons/gist/comments.svg';
const starIcon = '/Assets/icons/gist/star.svg';

import 'highlight.js/styles/atom-one-dark.css';
import { Prism as SyntaxHighlighter } from 'react-syntax-highlighter';
import { atomOneDark } from 'react-syntax-highlighter/dist/esm/styles/prism';
// import "highlight.js/styles/github-dark.css";

// import "./Gist.css";
const customTheme = {
  backgroundColor: "#011221",
  background: "transparent",
  padding: "0px",
  margin: "0px",
  borderRadius: "10px 10px 10px 10px",
  border: "1px solid #1e2d3d",
  fontSize: "12px",
};
interface GistSnippetProps {
  id: string;
}

const GistSnippet: React.FC<GistSnippetProps> = ({ id }) => {
  const [gist, setGist] = useState<GistData | null>(null);
  const [monthsAgo, setMonthsAgo] = useState<number | null>(null);
  const [content, setContent] = useState<string | null>(null);
  const [language, setLanguage] = useState<string | null>(null);
  const [dataFetched, setDataFetched] = useState<boolean>(false);
  const [comment, setComment] = useState<string | null>(null);

  const setValues = useCallback((gist: GistData) => {
    setGist(gist);
    setMonthsAgo(setMonths(gist.created_at));
    setContent(setSnippet(gist));
    if (gist.files) {
      const firstFile = Object.values(gist.files)[0];
      setLanguage(firstFile?.language || null);
    }
    setDataFetched(true);
    if (gist.comments_url) {
      setComments(gist.comments_url);
    }
  }, []);

  const setMonths = (date: string) => {
    const now = new Date();
    const gistDate = new Date(date);
    const diff = now.getTime() - gistDate.getTime();
    const days = Math.floor(diff / (1000 * 3600 * 24));
    const months = Math.floor(days / 30);
    return months;
  };

  const setSnippet = (gist: GistData) => {
    if (gist.files) {
      const firstFile = Object.values(gist.files)[0];
      return firstFile?.content || "";
    }
    return "";
  };

  const setComments = async (comments_url: string) => {
    try {
      const response = await fetch(comments_url);
      const data = await response.json();
      const body = data[0].body;
      setComment(body);
    } catch (error) {
      console.log(`No comments found on ${comments_url}`);
    }
  };

  const showComment = (id: string) => {
    const comment = document.getElementById("comment" + id);
    comment?.classList.toggle("hidden");
  };

  useEffect(() => {
    fetch(`https://api.github.com/gists/${id}`)
      .then((response) => response.json())
      .then((data) => {
        setValues(data);
      });
  }, [id, setValues]);

  useEffect(() => {
    hljs.highlightAll();
  }, [content]);

  return (
    <div
      className="gist mb-5"
      style={{ display: dataFetched ? "block" : "none" }}
    >
      <div className="flex justify-between my-2">
        <div className="flex">
          {/* Image */}
          <Image
            src={gist ? gist?.owner?.avatar_url : ""}
            width={32}
            height={32}
            alt="gist"
            className="w-8 h-8 rounded-full mr-2"
          />
          {/* userName */}
          <div className="flex flex-col">
            <a
              id="username"
              href={gist ? `https://github.com/${gist?.owner?.login}` : ""}
              target="_blank"
              rel="noopener noreferrer"
              className="font-fira_bold text-purple-text text-xs pb-1 hover:cursor-pointer"
            >
              @{gist ? gist?.owner?.login : ""}
            </a>
            <p className="font-fira_retina text-xs text-menu-text">
              Created {monthsAgo} months ago
            </p>
          </div>
        </div>

        {/* Details and comment icons */}
        <div className="flex text-menu-text font-fira_retina text-xs justify-self-end lg:mx-2">
          <div className="flex lg:mx-2 hover:cursor-pointer hover:text-white">
            <Image src={commentIcon} alt="gist" className="w-4 h-4 mr-2" />
            <span onClick={() => showComment(gist ? gist.id : "")}>
              details
            </span>
          </div>

          <div className="hidden lg:flex hover:cursor-pointer hover:text-white">
            <Image src={starIcon} alt="gist" className="w-4 h-4 mx-2" />
            <span className="">stars</span>
          </div>
        </div>
      </div>

      {/* Code Snippet */}
      {/* <pre>
        <code className={`hljs ${language}`}>{content ? content : ""}</code>
      </pre> */}

      <SyntaxHighlighter
        language={language}
        style={atomOneDark}
        customStyle={customTheme}
      >
        {content ? content : ""}
      </SyntaxHighlighter>

      {/* Comment Section */}
      <div
        id={`comment${gist ? gist.id : ""}`}
        className="flex hidden justify-between text-menu-text font-fira_retina mt-4 pt-4 border-top"
      >
        {comment ? (
          <p id="comment" className="w-5/6">
            {comment}
          </p>
        ) : (
          <p className="w-5/6">No comments.</p>
        )}
        <Image
          src={close}
          alt="gist"
          className="hover:cursor-pointer"
          onClick={() => showComment(gist ? gist.id : "")}
        />
      </div>
    </div>
  );
};

export default GistSnippet;
