
import {about} from '@/app/aboutme/data';
import Image from 'next/image';
import React from 'react';

const MiniSideMenu = ({ currentSection, focusCurrentSection }) => {
  return (
    <>
      <div id="sections" className=" hidden lg:block">
        {Object.keys(about.sections).map((section, index) => (
          <div
            id="section-icon"
            key={section}
            className={currentSection === section ? "active" : ""}
          >
            <Image
              id={"section-icon-" + section}
              src={about.sections[section].icon}
              alt={about.sections[section].title + "-section"}
              onClick={() => focusCurrentSection(section)}
            ></Image>
          </div>
        ))}
      </div>
    </>
  );
};

export default MiniSideMenu;
