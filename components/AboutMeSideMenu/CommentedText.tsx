
import {useEffect, useRef, useState} from 'react';
import './CodeContainer.css';

const CodeContainer = ({ text }) => {
  const [lines, setLines] = useState(0);
  const textContainerRef = useRef(null);

  const updateLines = () => {
    const textContainer = textContainerRef.current;
    const style = window.getComputedStyle(textContainer);
    const fontSize = parseInt(style.fontSize);
    const lineHeight = parseInt(style.lineHeight);
    const maxHeight = textContainer.offsetHeight;
    const newLines = Math.ceil(maxHeight / lineHeight) + 1;
    setLines(newLines);
  };

  useEffect(() => {
    updateLines();
    window.addEventListener("resize", updateLines);
    window.addEventListener("click", updateLines);
    return () => {
      window.removeEventListener("resize", updateLines);
      window.removeEventListener("click", updateLines);
    };
  }, []);

  return (
    <div className="code-container flex font-fira_retina text-menu-text">
      <div className="line-numbers lg:flex flex-col w-32 hidden">
        <div>
          {/* line numbers and asterisks */}
          {[...Array(lines)].map((_, index) => (
            <div className="grid grid-cols-2 justify-end" key={index + 1}>
              <span className="col-span-1 mr-3">{index + 1}</span>
              {index === 0 && (
                <div className="col-span-1 flex justify-center">{"/**"}</div>
              )}
              {index > 0 && index < lines - 1 && (
                <div className="col-span-1 flex justify-center">{"*"}</div>
              )}
              {index === lines - 1 && (
                <div className="col-span-1 flex justify-center pl-2">
                  {"*/"}
                </div>
              )}
            </div>
          ))}
        </div>
      </div>
      <div className="text-container" ref={textContainerRef}>
        <p dangerouslySetInnerHTML={{ __html: text }}></p>
      </div>
    </div>
  );
};

export default CodeContainer;
