
import {about} from '@/app/aboutme/data';
import Image from 'next/image';
const close = '/Assets/icons/close.svg';

const MiniHeader = ({ currentSection, folder }) => {
  return (
    <>
      <div className="tab-height w-full hidden lg:flex border-bot items-center">
        <div className="flex items-center border-right h-full">
          <p
            className="font-fira_regular text-menu-text text-sm px-3"
            dangerouslySetInnerHTML={{
              __html: about.sections[currentSection].title + " / " + folder,
            }}
          ></p>
          <Image src={close} alt="gist" className="mx-3"></Image>
        </div>
      </div>
    </>
  );
};

export default MiniHeader;
