

'use client';
import AnimatedBanner from '@/components/shared/AnimatedBanner/AnimatedBanner';
import {useCommonData} from '@/hooks/useCommonData';
import './index.css';
import {sendGAEvent} from '@next/third-parties/google';
import {useEffect} from 'react';

const Banner = () => {
    const {
        loading,
        error,
        getUserName,
        getCurrentCompany,
        getCurrentDesignation,
        getTotalExperience,
        getConfigGithubLink
    } = useCommonData();

    const initial = () => {
        sendGAEvent('event', 'page_visit', {
            category: 'Navigation',
            label: 'Initial Page Visit',
            value: 1,
        })
    }
    useEffect(() => {
    initial();
    }, []);

    // Loading state with skeleton animation
    if (loading) {
        return (
            <div id="hello">
                <div className="css-blurry-gradient-blue"></div>
                <div className="css-blurry-gradient-green"></div>
                <div className="hero">
                    <div className="head">
                        <span> Hi all, I am </span>
                        <h1 className="skeleton-text skeleton-name"></h1>
                        <h2 className="skeleton-text skeleton-title"></h2>
                    </div>
                    <div id="info">
                        <span className="action skeleton-text skeleton-experience"></span>
                        <span className="skeleton-text skeleton-github-line"></span>
                        <span className="skeleton-text skeleton-github-line"></span>
                        <span className="skeleton-text skeleton-github-line"></span>
                        <span className="skeleton-text skeleton-github-line"></span>
                        {/*<p className="skeleton-text skeleton-github-line"></p>*/}
                        {/*<p className="skeleton-text skeleton-github-line"></p>*/}
                    </div>
                </div>
                <div data-aos="fade-up" className="game sm:hidden lg:flex">
                    <AnimatedBanner/>
                </div>
            </div>
        );
    }

    // Error state
    if (error) {
        return (
            <div id="hello">
                <div className="css-blurry-gradient-blue"></div>
                <div className="css-blurry-gradient-green"></div>
                <div className="hero">
                    <div className="head">
                        <span> Hi all, I am </span>
                        <h1>Tanzim Ahmed</h1>
                        <h2>{'> Software Engineer'}</h2>
                    </div>
                    <div id="info">
            <span className="action">
              {'// Error loading data, showing defaults '}{' '}
            </span>
                        <span>{' // you can also see it on my Github page '}</span>
                        <span> {'// find my profile on Github: '}</span>
                        <p className="code">
                            <span className="identifier"> const </span>
                            <span className="variable-name"> githubLink </span>
                            <span className="operator"> = </span>
                            <a href={'https://github.com/tanzim077'} className="string">
                                "https://github.com/tanzim077"
                            </a>
                        </p>
                    </div>
                </div>
                <div data-aos="fade-up" className="game sm:hidden lg:flex">
                    <AnimatedBanner/>
                </div>
            </div>
        );
    }

    // Success state with dynamic data
    const userName = getUserName();
    const githubLink = getConfigGithubLink();
    const totalExperience = getTotalExperience();
    const currentCompany = getCurrentCompany();
    const currentDesignation = getCurrentDesignation();

    return (
        <>
            <div id="hello">
                <div className="css-blurry-gradient-blue"></div>
                <div className="css-blurry-gradient-green"></div>
                <div className="hero">
                    <div className="head">
                        <span> Hi all, I am </span>
                        <h1>{userName}</h1>
                        <h2>{`> ${currentDesignation}`}</h2>
                    </div>
                    <div id="info">
            <span className="action">
              {`// ${totalExperience} professional experience `}{' '}
            </span>
                        <span>{' // you can also see it on my Github page '}</span>
                        <span> {'// find my profile on Github: '}</span>
                        <p className="code">
                            <span className="identifier"> const </span>
                            <span className="variable-name"> githubLink </span>
                            <span className="operator"> = </span>
                            <a href={githubLink} className="string">
                                "{githubLink}"
                            </a>
                        </p>
                    </div>
                </div>

                <div data-aos="fade-up" className="game sm:hidden lg:flex">
                    <AnimatedBanner/>
                </div>
            </div>
        </>
    );
};

export default Banner;
