
#hello {
    display: flex;
    height: 100%;
    width: 100%;
    flex: 1 1 auto;
    padding-left: 275px;
    overflow: hidden;
    /* padding-top: 5rem; */
    /* 80px */
}

.hero {
    width: 100% ;
    justify-content: center;
    align-items: baseline !important;

}

.game {
    display: flex;
    flex-direction: column;
    width: 100%;
    height: 100%;
    justify-content: center;
    /* 	align-items: center; */
    z-index: 20;
}

#hello .hero {
    display: flex;
    flex-direction: column;
    /* display: grid;
	grid-template-columns: repeat(12, minmax(0, 1fr)); */
    margin: 0rem;
}


#hello .head span {
    font-size: 18px;
    line-height: 1;
    color: #E5E9F0;
    font-family: 'Fira Code';
}

#hello .head h1 {
    font-size: 58px;
    line-height: 1;
    color: #E5E9F0;
    font-family: 'Fira Code';
    padding-top: 1rem;
    /* 16px */
    padding-bottom: 1rem;
    /* 16px */
}

#hello .head h2 {
    font-size: 32px;
    line-height: 1;
    color: #4D5BCE;
    font-family: 'Fira Code';
}

.head {
    padding-bottom: 3rem;
}

#info {
    display: flex;
    flex-direction: column;
}

#info>span {
    font-size: 14px;
    line-height: 1;
    color: #607B96;
    font-family: 'Fira Code';
    padding-bottom: 1rem;
    /* 16px */
}

.code {
    font-family: 'Fira Code';
    color: #E5E9F0;
}

.code .identifier {
    color: #4D5BCE;
}

.code .variable-name {
    color: #43D9AD;
}

.code .operator {
    color: white;
}

.code .string {
    color: #E99287;
    text-decoration-line: underline;
    text-underline-offset: 4px;
}

#info {
    padding-block: 2.5rem;
}

#info .action {
    display: flex
}

.hide {
    display: none;
}

.css-blurry-gradient-blue {
    position: fixed;
    bottom: 25%;
    right: 5%;
    width: 300px;
    height: 300px;
    border-radius: 0% 0% 50% 50%;
    rotate: 10deg;
    filter: blur(70px);
    background: radial-gradient(circle at 50% 50%, rgba(77, 91, 206, 1), rgba(76, 0, 255, 0));
    opacity: 0.5;
    z-index: 10;
}

.css-blurry-gradient-green {
    position: absolute;
    top: 20%;
    right: 30%;
    width: 300px;
    height: 300px;
    border-radius: 0% 50% 0% 50%;
    filter: blur(70px);
    background: radial-gradient(circle at 50% 50%, rgba(67, 217, 173, 1), rgba(76, 0, 255, 0));
    opacity: 0.5;
    z-index: 10;
}

#info {
    font-size: 14px;
}


/* mobile */
@media (max-width: 768px) {

    #hello {
        padding-left: 0;
    }

    #hello .hero {
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        margin: 1.75rem;
        /* 28px */
    }

    .head {
        padding-top: 4rem;
        /* 40px */
    }

    #hello .head h2 {
        font-size: 20px;
        color: #43D9AD;
    }

    #info .action {
        display: none;
    }

}

/* tablet */
@media (min-width: 768px) and (max-width: 1024px) {
    #hello {
        padding-left: 0;
    }

    #hello .hero {
        display: flex;
        flex-direction: column;
        justify-content: center;
        margin: 1.75rem;
        /* 28px */
    }

    .head {
        padding-top: 4rem;
        /* 40px */
    }

}

@media (min-width: 1024px) and (max-width: 1320px) {
    #hello {
        padding-left: 135px;
    }
}


/* LG */

@media (min-width: 1024px) {

    .css-blurry-gradient-blue {
        position: fixed;
        bottom: 10%;
        right: 10%;
        width: 500px;
        height: 500px;
        opacity: 0.7;
        border-radius: 100% 50% 100% 0%;
    }

    .css-blurry-gradient-green {
        position: fixed;
        top: 10%;
        right: 35%;
        filter: blur(100px);
        rotate: 10deg;
        width: 400px;
        height: 400px;
        opacity: 0.5;
        border-radius: 100% 0% 0% 0%;
        rotate: 20deg;
    }
}

@media (min-width: 1920px) {
    #hello {
        padding-left: 310px;
    }

    #hello .head h1 {
        font-size: 62px;
    }
}


#console {
    width: 530px;
    height: 475px;
    border: 1px solid black;
    display: flex;
    align-items: center;
    justify-content: space-between;
    background: linear-gradient(to bottom, rgba(35, 123, 109, 1), rgba(67, 217, 173, 0.13));
    border-radius: 10px;
    padding: 30px;
    position: relative;
}

#game-screen {
    width: 240px;
    height: 400px;
    border-radius: 10px;
    background-color: rgba(1, 22, 39, 0.84);
    display: flex;
    flex-wrap: wrap;
    box-shadow: inset 0 0 10px #00000071;
}

#start-button {
  padding-inline: 16px;
  padding-block: 8px;
  border-radius: 10px;
  border: 1px solid black;
  background-color: #FEA55F;
  color: black;
  cursor: pointer;
  position: absolute;
  bottom: 20%;
  left: 17%;
  font-size: 0.875rem; /* 14px */
  line-height: 1.25rem; /* 20px */
}

#start-button:hover {
  background-color: rgb(255, 178, 119);
}

#console-menu{
  height: 400px;
}

#console-button {
  background-color: #010C15;
  border-radius: 10px;
  display: flex;
  justify-content: center;
  align-items: center;
  width: 50px;
  height: 30px;
}

#console-button:hover {
  background-color: #010c15d8;
  box-shadow: #43D9AD 0 0 10px;
}

#instructions {
  background-color: rgba(1, 20, 35, 0.19);
  border-radius: 7px;
  padding: 10px;
}

.food {
  background-color: #43D9AD;
  border-radius: 50%;
  box-shadow: 0 0 10px #43D9AD;
  width: 8px;
  height: 8px;
  opacity: 0.3;
}

#game-over, #congrats {
  position: absolute;
  bottom: 12%;
  color: #43D9AD;
  width: 240px;
}

#game-over, #congrats > span {
  font-size: 1.5rem; /* 24px */
  line-height: 2rem; /* 32px */
}

#corner {
  width: 24px;
  height: 24px;
}

#skip-btn{
  font-size: 14px;
  color: white;
  padding-inline: 16px;
  padding-block: 8px;
  border: 2px solid white;
  border-radius: 0.5rem; /* 8px */
}


@media (min-width: 1024px) and (max-width: 1536px) {
  #game-screen {
    width: 192px;
    height: 320px;
  }

  #console {
    width: 420px;
    height: 370px;
    padding: 24px;

  }

  #start-button {
  padding-inline: 12px;
  padding-block: 6px;
  border-radius: 8px;
  bottom: 20%;
  left: 17%;
  font-size: 0.75rem; /* 14px */
  line-height: 1rem; /* 20px */
}

  #console-menu{
  height: 320px;
}

#instructions {
  font-size: 12px;
}

#console-button {
  width: 40px;
  height: 25px;
  border-radius: 6px;
}

#score-board {
  font-size: 12px;
}

.food {
  width: 6px;
  height: 6px;
}

#game-over, #congrats {
  position: absolute;
  bottom: 10%;
  color: #43D9AD;
  width: 192px;
}

#game-over, #congrats > span {
  font-size: 1.125rem; /* 18px */
  line-height: 1.75rem; /* 28px */
}

#corner {
  width: 20px;
  height: 20px;
}

#skip-btn{
  font-size: 12px;
  padding-inline: 12px;
  padding-block: 6px;
  border: 2px solid white;
  border-radius: 0.5rem; /* 8px */
}
}

/* Skeleton Loading Animation - Updated for dark theme consistency */
.skeleton-text {
  background: linear-gradient(90deg, #374151 25%, #4B5563 50%, #374151 75%);
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s infinite;
  border-radius: 4px;
  opacity: 0.8;
  display: block;
}

/* Skeleton styles with higher specificity - Dark theme colors */
h1.skeleton-name {
  width: 350px !important;
  height: 58px !important;
  background: linear-gradient(90deg, #374151 25%, #4B5563 50%, #374151 75%) !important;
  background-size: 200% 100% !important;
  animation: skeleton-loading 1.5s infinite !important;
  border-radius: 4px !important;
  color: transparent !important;
  font-size: inherit !important;
  line-height: inherit !important;
  padding: inherit !important;
  margin: inherit !important;
  margin-bottom: 8px !important;
  opacity: 0.8 !important;
}

h2.skeleton-title {
  width: 280px !important;
  height: 32px !important;
  background: linear-gradient(90deg, #374151 25%, #4B5563 50%, #374151 75%) !important;
  background-size: 200% 100% !important;
  animation: skeleton-loading 1.5s infinite !important;
  border-radius: 4px !important;
  color: transparent !important;
  font-size: inherit !important;
  line-height: inherit !important;
  padding: inherit !important;
  margin: inherit !important;
  margin-bottom: 12px !important;
  opacity: 0.8 !important;
}

span.skeleton-experience {
  width: 320px !important;
  height: 14px !important;
  background: linear-gradient(90deg, #374151 25%, #4B5563 50%, #374151 75%) !important;
  background-size: 200% 100% !important;
  animation: skeleton-loading 1.5s infinite !important;
  border-radius: 4px !important;
  color: transparent !important;
  display: inline-block !important;
  margin-bottom: 0px !important;
  opacity: 0.8 !important;
}

span.skeleton-github-line {
  width: 280px !important;
  height: 14px !important;
  background: linear-gradient(90deg, #374151 25%, #4B5563 50%, #374151 75%) !important;
  background-size: 200% 100% !important;
  animation: skeleton-loading 1.5s infinite !important;
  border-radius: 4px !important;
  color: transparent !important;
  display: inline-block !important;
  margin-bottom: 8px !important;
  opacity: 0.8 !important;
}

p.skeleton-github-link {
  width: 240px !important;
  height: 14px !important;
  background: linear-gradient(90deg, #374151 25%, #4B5563 50%, #374151 75%) !important;
  background-size: 200% 100% !important;
  animation: skeleton-loading 1.5s infinite !important;
  border-radius: 4px !important;
  color: transparent !important;
  font-size: inherit !important;
  line-height: inherit !important;
  padding: inherit !important;
  margin: inherit !important;
  opacity: 0.8 !important;
}

@keyframes skeleton-loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

/* Mobile skeleton adjustments - match mobile CSS */
@media (max-width: 768px) {
  h1.skeleton-name {
    width: 250px !important;
    height: 58px !important; /* Keep same height as desktop since mobile doesn't change h1 font-size */
  }

  h2.skeleton-title {
    width: 180px !important;
    height: 20px !important; /* Match mobile h2 font-size: 20px */
  }

  /* Hide the action span on mobile since .action is display: none */
  span.action.skeleton-experience {
    display: none !important;
  }

  span.skeleton-github-line {
    width: 200px !important;
    height: 14px !important;
  }

  p.skeleton-github-link {
    width: 180px !important;
    height: 14px !important;
  }
}

/* Tablet skeleton adjustments */
@media (min-width: 768px) and (max-width: 1024px) {
  h1.skeleton-name {
    width: 320px !important;
    height: 58px !important;
  }

  h2.skeleton-title {
    width: 250px !important;
    height: 32px !important;
  }

  span.skeleton-experience {
    width: 280px !important;
    height: 14px !important;
  }

  span.skeleton-github-line {
    width: 250px !important;
    height: 14px !important;
  }

  p.skeleton-github-link {
    width: 220px !important;
    height: 14px !important;
  }
}

/* Large screen adjustments */
@media (min-width: 1920px) {
  h1.skeleton-name {
    width: 380px !important;
    height: 62px !important; /* Match larger h1 font-size: 62px */
  }
}