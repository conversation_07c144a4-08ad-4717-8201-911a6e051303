.experience-card {
  background-color: #011221;
  border: 1px solid #1e2d3d;
  border-radius: 15px;
  @apply font-fira_regular;
}
.exp-designation {
  /* font-size: 20px; */
  font-weight: 500;
  color: #fff;
}
.exp-company {
  /* font-size: 16px; */
  font-weight: 450;
  color: #8f77e6;
}
.exp-duration {
  font-size: 14px;
  font-weight: 450;
  color: #fff;
}

.exp-timeline-btn {
  display: flex;
  align-items: center;
  align-content: center;
  background-color: #1c2b3a;
  color: #fff;
  border-radius: 8px;
  padding: 5px 10px;
  font-size: 14px;
  font-weight: 450;
  width: 280px;
  cursor: pointer;
}
.exp-active-timeline-btn {
  display: flex;
  align-items: center;
  align-content: center;
  background-color: #182457;
  color: #fff;
  border-radius: 8px;
  padding: 5px 10px;
  font-size: 14px;
  font-weight: 450;
  width: 280px;
  cursor: pointer;
}

.content-image {
  height: 100px;
  width: 100px;
  border-radius: 15px 0 0 0;
  background-color: #1c2b3a;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
}

.edu-title {
  font-size: 18px;
  font-weight: 450;
}

.timeline-btn {
  display: flex;
  align-items: center;
  align-content: center;
  background-color: #1c2b3a;
  color: #fff;
  border-radius: 8px;
  padding: 5px 10px;
  font-size: 14px;
  font-weight: 450;
  width: 250px;
  cursor: pointer;
}

.border-down {
  border-bottom: 1px solid #1e2d3d;
}
@media (max-width: 768px) {
  .experience-card {
    width: 100%;
    text-align: justify;
    font-size: 14px;
  }
  .edu-title {
    font-size: 15px;
    font-weight: 450;
  }
  .content-image {
    height: 50px;
    width: 50px;
    border-radius: 15px 0 0 0;
    background-color: #1c2b3a;
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
  }
  .timeline {
  }
  .timeline-btn {
    width: 100%;
    display: block;
    text-align: center;
  }
}
