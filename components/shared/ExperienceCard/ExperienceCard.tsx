
import Image from 'next/image';
import './index.css';
import {defaultImage} from '@/utils/constants';

export const dateRangeModifier = (startedAt, endedAt) => {
  const options = { year: "numeric", month: "long" };
  const startDate = new Date(startedAt).toLocaleDateString("en-US", options);
  const endDate = !endedAt ? "Present" : new Date(endedAt).toLocaleDateString("en-US", options);
  return `${startDate} - ${endDate}`;
};
const ExperienceCard = ({ item }) => {
  return (
    <div className="experience-card ">
      <div className="flex gap-5 border-down items-center">
        {/* insert an image here for next js  */}
        <Image
          className="content-image"
          style={{ cursor: "pointer" }}
          width={100}
          height={100}
          src={item?.logo || defaultImage}

          alt="experience"
          onClick={() => window.open(item.companyWebsite, "_blank")}
        />
        <div>
          <span>
            <span className="exp-designation">{item.designation}</span>
            <span style={{ fontStyle: "italic" }}> at </span>
            <span
              style={{ cursor: "pointer" }}
              className="exp-company"
              onClick={() => window.open(item.companyWebsite, "_blank")}
            >
              {item.name}
            </span>
          </span>
          <p className="edu-title">{item.address}</p>
          <span className={!item.endedAt ? "exp-active-timeline-btn " : "exp-timeline-btn"}>
            {dateRangeModifier(item.startedAt, item.endedAt)}
          </span>
        </div>
      </div>

      <div className="timeline p-4 lg:p-6 ">
        <span className="">{item.companyDescription}</span>
        <br />
        <br />
      </div>
    </div>
  );
};

export default ExperienceCard;
