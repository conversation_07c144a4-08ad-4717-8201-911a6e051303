 .education-card{
  background-color: #011221;
  border: 1px solid #1E2D3D;
  border-radius: 15px;
  @apply font-fira_regular
}

.edu-title{
  font-size: 20px;
  font-weight: 450
}

.divider-horizontal{
  color: #5565E8;
}

.body-box{
  padding: 20px;
  height: 100%;
  width: 100%;
  display: flex;
  justify-content: space-between;
}

.timeline-btn{
  display: flex;
  align-items: center;
  align-content: center;
  background-color: #1C2B3A;
  color: #fff;
  border-radius: 8px;
  padding: 5px 10px;
  font-size: 14px;
  font-weight: 450;
  width: 160px;
  cursor: pointer;
}


.content-image{
  height: 100px;
  width: 100px;
  border-radius: 15px 0 0 0;
  background-color: #1C2B3A;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
}

.edu-title{
  font-size: 18px;
  font-weight: 450;
}

.border-down{
  border-bottom: 1px solid #1E2D3D;
}
@media (max-width : 768px){
    
    .edu-title{
        font-size: 15px;
        font-weight: 450;
    }
    .content-image{
        height: 50px;
        width: 50px;
        border-radius: 15px 0 0 0;
        background-color: #1C2B3A;
        background-size: cover;
        background-position: center;
        background-repeat: no-repeat;
    }
    
    .timeline-btn{
        width: 100%;
        display: block;
        text-align: center;
    }
    
}