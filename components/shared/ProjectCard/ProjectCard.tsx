/*
 * Filename: /home/<USER>/WorkStation/myportfolio/components/shared/ProjectCard/ProjectCard.jsx
 * Path: /home/<USER>/WorkStation/myportfolio
 * Created Date: Saturday, April 15th 2023, 12:59:21 pm
 * Author: <PERSON><PERSON><PERSON>
 *
 * Copyright (c) 2023 Tanzi<PERSON> Ahmed
 */
import Image from 'next/image';
import React from 'react';
const github = '/Assets/images/github.svg';
import Link from 'next/link';

const ProjectCard = ({ item, index }) => {
  return (
    <div>
      <br />
      <div className="project-card">
        <div className="project-image border-down"></div>

        <div className="px-6 py-3">
          <span>
            <span
              className="body-1"
              style={{ color: "#5565e8", fontWeight: 600 }}
            >
              Project {index + 1}
            </span>
            <span style={{ color: "#5565e8", fontWeight: 600 }}>
              : {item.title}
            </span>
          </span>
          <br />
          <span className="proj-desc">{item.description}</span>
          <br />
          <br />
          <Link href={item.link} target="blank">
            <span className="timeline-btn ">
              <Image src={github} alt="tanzim077" />
              <span className="px-2"> Source code </span>
            </span>
          </Link>
        </div>
      </div>
    </div>
  );
};

export default ProjectCard;
