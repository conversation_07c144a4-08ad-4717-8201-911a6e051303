.slideshow-dots {
  position: absolute;
  bottom: 10px;
  left: 0;
  right: 0;
  display: flex;
  justify-content: center;
  z-index: 10;
}

.dots-container {
  display: flex;
  gap: 6px;
  background-color: rgba(1, 18, 33, 0.6);
  padding: 4px 8px;
  border-radius: 12px;
}

.dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: rgba(96, 123, 150, 0.5); /* Using your menu-text color with opacity */
  cursor: pointer;
  transition: all 0.3s ease;
}

.dot.active {
  background-color: #43D9AD; /* Using your greenfy color */
  transform: scale(1.2);
}

/* Add a subtle hover effect */
.dot:hover {
  background-color: rgba(96, 123, 150, 0.8);
  transform: scale(1.1);
}

/* Skill logos styling */
.skill-logos-container {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
  max-width: 70%;
  justify-content: flex-end;
}

.skill-logos-container img {
  background-color: rgba(1, 18, 33, 0.7);
  border-radius: 50%;
  padding: 3px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  transition: all 0.2s ease;
  width: 24px !important;
  height: 24px !important;
  object-fit: contain;
  border: 1px solid rgba(67, 217, 173, 0.3); /* Light greenfy border */
}

.skill-logos-container img:hover {
  transform: scale(1.2);
  background-color: rgba(1, 18, 33, 0.9);
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.4);
  border-color: rgba(67, 217, 173, 0.8); /* Brighter greenfy border on hover */
}
