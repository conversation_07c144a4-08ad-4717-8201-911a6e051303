"use client";
import Image from 'next/image';
import {useEffect, useState} from 'react';

const ImageSlideshow = ({ images, interval = 3000, skills = [] }) => {
  const [currentIndex, setCurrentIndex] = useState(0);

  // For multiple images, implement slideshow
  useEffect(() => {
    // Only set up the timer if we have multiple images
    if (images && images.length > 1) {
      const timer = setInterval(() => {
        setCurrentIndex((prevIndex) => (prevIndex + 1) % images.length);
      }, interval);

      return () => clearInterval(timer);
    }
  }, [images, interval]);

  // Handle case where images is empty or undefined
  if (!images || !Array.isArray(images) || images.length === 0) {
    return (
      <div style={{ position: 'relative', width: '100%', height: '120px', borderTopLeftRadius: '15px', borderTopRightRadius: '15px', overflow: 'hidden', backgroundColor: '#011627' }}>
        {/* Skill logos overlay */}
        {skills && Array.isArray(skills) && skills.length > 0 && (
          <div className="absolute flex right-3 top-3 z-10 skill-logos-container">
            {skills.map((skill, idx) => (
              <Image
                key={skill.id || skill._id || idx}
                width={24}
                height={24}
                src={skill.logo || '/images/tech-icon.png'}
                alt={skill.name || ''}
                className="hover:opacity-100"
              />
            ))}
          </div>
        )}
      </div>
    );
  }

  // If only one image, just display it without slideshow functionality
  if (images.length === 1) {
    return (
      <div style={{ position: 'relative', width: '100%', height: '120px', borderTopLeftRadius: '15px', borderTopRightRadius: '15px', overflow: 'hidden' }}>
        <Image
          id="showcase"
          fill={true}
          sizes="(max-width: 768px) 100vw, 400px"
          style={{ objectFit: 'cover' }}
          src={images[0]}
          alt="Project showcase"
        />
        {/* Skill logos overlay */}
        {skills && Array.isArray(skills) && skills.length > 0 && (
          <div className="absolute flex right-3 top-3 z-10 skill-logos-container">
            {skills.map((skill, idx) => (
              <Image
                key={skill.id || skill._id || idx}
                width={24}
                height={24}
                src={skill?.logo || '/images/tech-icon.png'}
                alt={skill.name || ''}
                className="hover:opacity-100"
              />
            ))}
          </div>
        )}
      </div>
    );
  }

  // For multiple images, render the slideshow
  return (
    <div style={{ position: 'relative', width: '100%', height: '120px', borderTopLeftRadius: '15px', borderTopRightRadius: '15px', overflow: 'hidden' }}>
      {images.map((image, index) => (
        <Image
          key={index}
          id="showcase"
          fill={true}
          sizes="(max-width: 768px) 100vw, 400px"
          style={{
            objectFit: 'cover',
            opacity: index === currentIndex ? 1 : 0,
            transition: 'opacity 0.5s ease-in-out'
          }}
          src={image}
          alt={`Project showcase ${index + 1}`}
        />
      ))}

      {/* Skill logos overlay - always visible */}
      {skills && Array.isArray(skills) && skills.length > 0 && (
        <div className="absolute flex right-3 top-3 z-10 skill-logos-container">
          {skills.map((skill, idx) => (
            <Image
              key={skill._id || skill.id || idx}
              width={24}
              height={24}
              src={skill.logo || '/images/tech-icon.png'}
              alt={skill.name || ''}
              className="hover:opacity-100"
            />
          ))}
        </div>
      )}

      {/* Navigation dots */}
      <div className="slideshow-dots">
        <div className="dots-container">
          {images.map((_, index) => (
            <span
              key={index}
              className={`dot ${index === currentIndex ? 'active' : ''}`}
              onClick={() => setCurrentIndex(index)}
            />
          ))}
        </div>
      </div>
    </div>
  );
};

export default ImageSlideshow;
