/*
 * Example component showing how to use the new getConfigImage() function
 * This demonstrates how to access the configuration image from the common-data API
 */

'use client';
import {useCommonData} from '@/hooks/useCommonData';
import Image from 'next/image';

const ConfigImageExample = () => {
    const {
        loading,
        error,
        getConfigImage,
        getUserProfileImage
    } = useCommonData();

    if (loading) {
        return (
            <div className="p-4">
                <div className="animate-pulse">
                    <div className="bg-gray-300 h-48 w-full rounded-lg mb-4"></div>
                    <div className="bg-gray-300 h-4 w-3/4 rounded mb-2"></div>
                    <div className="bg-gray-300 h-4 w-1/2 rounded"></div>
                </div>
            </div>
        );
    }

    if (error) {
        return (
            <div className="p-4 text-red-500">
                Error loading images: {error}
            </div>
        );
    }

    const configImage = getConfigImage();
    const profileImage = getUserProfileImage();

    return (
        <div className="p-4 space-y-6">
            <h2 className="text-2xl font-bold">Image Examples</h2>
            
            {/* Configuration Image */}
            <div>
                <h3 className="text-lg font-semibold mb-2">Configuration Image</h3>
                {configImage ? (
                    <div className="relative w-full h-48 rounded-lg overflow-hidden">
                        <Image
                            src={configImage}
                            alt="Configuration Image"
                            fill
                            className="object-cover"
                            onError={(e) => {
                                console.error('Failed to load config image:', configImage);
                            }}
                        />
                    </div>
                ) : (
                    <div className="w-full h-48 bg-gray-200 rounded-lg flex items-center justify-center">
                        <p className="text-gray-500">No configuration image available</p>
                    </div>
                )}
                <p className="text-sm text-gray-600 mt-2">
                    Source: {configImage || 'Not set in configuration'}
                </p>
            </div>

            {/* Profile Image */}
            <div>
                <h3 className="text-lg font-semibold mb-2">Profile Image</h3>
                {profileImage ? (
                    <div className="relative w-32 h-32 rounded-full overflow-hidden">
                        <Image
                            src={profileImage}
                            alt="Profile Image"
                            fill
                            className="object-cover"
                            onError={(e) => {
                                console.error('Failed to load profile image:', profileImage);
                            }}
                        />
                    </div>
                ) : (
                    <div className="w-32 h-32 bg-gray-200 rounded-full flex items-center justify-center">
                        <p className="text-gray-500 text-sm text-center">No profile image</p>
                    </div>
                )}
                <p className="text-sm text-gray-600 mt-2">
                    Source: {profileImage || 'Not set in user profile'}
                </p>
            </div>

            {/* API Structure Info */}
            <div className="bg-blue-50 p-4 rounded-lg">
                <h3 className="text-lg font-semibold mb-2">API Response Structure</h3>
                <div className="space-y-2 text-sm">
                    <p><strong>Configuration Image:</strong> <code>data.config.image</code> → <code>getConfigImage()</code></p>
                    <p><strong>GitHub URL:</strong> <code>data.config.githubURL</code> → <code>getConfigGithubLink()</code></p>
                    <p><strong>Profile Image:</strong> <code>data.user.profileImage</code> → <code>getUserProfileImage()</code></p>
                    <p><strong>Config Object:</strong> Contains all configuration settings including theme, websiteUrl, etc.</p>
                </div>
            </div>

            {/* Usage Instructions */}
            <div className="bg-green-50 p-4 rounded-lg">
                <h3 className="text-lg font-semibold mb-2">Usage Instructions</h3>
                <div className="space-y-2 text-sm">
                    <p><strong>Configuration Image:</strong> Use <code>getConfigImage()</code> for banners, hero sections, or site-wide images</p>
                    <p><strong>Profile Image:</strong> Use <code>getUserProfileImage()</code> for user avatars and profile pictures</p>
                    <p><strong>GitHub Link:</strong> Use <code>getConfigGithubLink()</code> for the preferred GitHub URL from configuration</p>
                </div>
            </div>
        </div>
    );
};

export default ConfigImageExample;
