import {gists} from '@/app/aboutme/data';
import GistSnippet from '@/components/AboutMeSideMenu/GistSnippet';
import React from 'react';

const RightSideBox = () => {
  return (
    <>
      <div id="right" className="max-w-full flex flex-col">
        <div className="tab-height w-full h-full hidden lg:flex border-bot items-center"></div>
        <div className="tab-height w-full h-full flex-none lg:hidden items-center"></div>
        <div id="gists-content" className="flex">
          <div
            id="gists"
            className="flex flex-col lg:px-6 lg:py-4 w-full overflow-hidden"
          >
            <h3 className="text-white lg:text-menu-text mb-4 text-sm">
              {"// Code snippet showcase:"}
            </h3>
            <div className="flex  flex-col overflow-scroll">
              {Object.keys(gists).map((gist, index) => (
                <GistSnippet
                  data-aos="fade-down"
                  id={gists[gist]}
                  key={index}
                ></GistSnippet>
              ))}
            </div>
          </div>
          <div
            id="scroll-bar"
            className="h-full border-left hidden lg:flex justify-center py-1"
          >
            <div id="scroll"></div>
          </div>
        </div>
      </div>
    </>
  );
};

export default RightSideBox;
