form {
    @apply font-fira_retina text-menu-text
}

input {
    background-color: #011221;
    border: 2px solid #1E2D3D;
    border-radius: 7px;

}

/* Change Autocomplete styles in Chrome*/
input:-webkit-autofill,
input:-webkit-autofill:hover,
input:-webkit-autofill:focus,
textarea:-webkit-autofill,
textarea:-webkit-autofill:hover,
textarea:-webkit-autofill:focus,
select:-webkit-autofill,
select:-webkit-autofill:hover,
select:-webkit-autofill:focus {
    -webkit-text-fill-color: rgb(190, 190, 190);
    transition: background-color 5000s ease-in-out 0s;
    border: 2px solid #607b96;
}

#message-input {
    background-color: #011221;
    border: 2px solid #1E2D3D;
    border-radius: 7px;
    resize: none;
    height: 150px;
    padding: 10px;
}

#submit-button {
    @apply font-fira_retina text-white text-sm;
    background-color: #1E2D3D;
    border-radius: 7px;
    margin-top: 20px;
    cursor: pointer;
}

#submit-button:hover {
    background-color: #263B50;
}

input:focus,
#message-input:focus {
    outline: none;
    transition: none;
    border: 2px solid #607b96;
    box-shadow: #607b9669 0px 0px 0px 2px;
}

#contact-form {
    width: 100%;
}

@media (max-width: 768px) {
    
    #contact-form {
        min-width: 80vw;
    }
}


@media (min-width: 768px) and (max-width: 1920px) {
    #contact-form {
    width: 100%;
    min-width: 500px;
    }

    #submit-button {
        font-size: 12px;
    }

    textarea {
        font-size: 13px;
        max-height: 130px !important;
    }

    input {
        font-size: 13px;
    }
}