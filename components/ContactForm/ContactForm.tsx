
import emailjs from '@emailjs/browser';
import {useRef} from 'react';
import './index.css';

const ContactForm = ({ name, email,title, message, configData, setName,setTitle, setEmail, setMessage }) => {
const {
  emailjsServiceId,
  emailjsTemplateId,
  emailjsPublicKey,
  emailjsPrivateKey,
} = configData[0] || {};
const form = useRef();

  const sendEmail =async (e) => {
    e.preventDefault();
    const data = {
      name : name || '',
      email : email || '',
      title : title || '',
      message : message || ''
    }

    emailjs
      .sendForm(
        emailjsServiceId,
        emailjsTemplateId,
        form.current,
        {
          publicKey: emailjsPublicKey,
        }
      )
      .then(
        () => {
          console.log("SUCCESS!");
          // Reset form after successful submission
          if (setName) setName("");
          if (setEmail) setEmail("");
          if (setTitle) setTitle("");
          if (setMessage) setMessage("");
          form.current.reset();
        },
        (error) => {
          console.log("FAILED...", error.text);
        }
      );
  };
  return (
    <div>
      <form id="contact-form" className="text-sm" ref={form} onSubmit={sendEmail}>
        <div className="flex flex-col">
          <label htmlFor="name" className="mb-3">
            _name:
          </label>
          <input
            type="text"
            id="name-input"
            name="name"
            value={name || ""}
            onChange={(e) => setName && setName(e.target.value)}
            placeholder="Enter your name"
            required
            className="p-2 mb-5 placeholder-slate-600"
          />
        </div>
        <div className="flex flex-col">
          <label htmlFor="email" className="mb-3">
            _email:
          </label>
          <input
            type="email"
            id="email-input"
            name="email"
            value={email || ""}
            onChange={(e) => setEmail && setEmail(e.target.value)}
            placeholder="Enter your email"
            required
            className="p-2 mb-5 placeholder-slate-600"
          />
        </div>
        <div className="flex flex-col">
          <label htmlFor="name" className="mb-3">
            _title:
          </label>
          <input
              type="text"
              id="title-input"
              name="title"
              value={title || ""}
              onChange={(e) => setTitle && setTitle(e.target.value)}
              placeholder="Enter Email Title"
              required
              className="p-2 mb-5 placeholder-slate-600"
          />
        </div>
        <div className="flex flex-col">
          <label htmlFor="message" className="mb-3">
            _message:
          </label>
          <textarea
            id="message-input"
            name="message"
            value={message || ""}
            onChange={(e) => setMessage && setMessage(e.target.value)}
            placeholder="Enter your message"
            required
            className="placeholder-slate-600"
          ></textarea>
        </div>
        <button id="submit-button" type="submit" className="py-2 px-4">
          submit-message
        </button>
      </form>
    </div>
  );
};

export default ContactForm;
