

import React, {useEffect, useState} from 'react';
import './index.css';

const FormContent = ({ name, email, message }) => {
  const [lines, setLines] = useState(0);

  const updateLines = () => {
    const textContainer = document.querySelector(".text-container");
    if (textContainer) {
      const style = window.getComputedStyle(textContainer);
      const fontSize = parseInt(style.fontSize);
      const lineHeight = parseInt(style.lineHeight);
      const maxHeight = textContainer.offsetHeight;
      const numberOfLines = Math.ceil(maxHeight / lineHeight);
      setLines(numberOfLines);
    }
  };

  useEffect(() => {
    updateLines();
    window.addEventListener("resize", updateLines);
    window.addEventListener("input", updateLines);
    window.addEventListener("click", updateLines);

    return () => {
      window.removeEventListener("resize", updateLines);
      window.removeEventListener("click", updateLines);
      window.removeEventListener("input", updateLines);
    };
  }, []);

  const currentDate = new Date().toDateString();
  return (
    <>
      <div className="code-container flex font-fira_retina text-menu-text">
        <div className="line-numbers lg:flex flex-col w-16 hidden">
          {/* line numbers and asterisks */}
          {[...Array(lines)].map((_, index) => (
            <div className="grid grid-cols-2 justify-end" key={index + 1}>
              <span className="col-span-1 mr-3">{index + 1}</span>
            </div>
          ))}
        </div>
        <div className="font-fira_retina text-white text-container">
          <p>
            <span className="tag">const</span>{" "}
            <span className="tag-name">button</span> ={" "}
            <span className="tag-name">
              document.querySelector
              <span className="text-menu-text">("#sendBtn");</span>
            </span>
          </p>
          <br />
          <p className="text-menu-text">
            <span className="tag">const</span>{" "}
            <span className="tag-name">message</span> = {"{"}
            <br />
            &nbsp;&nbsp;
            <span id="name" className="tag-name">
              name
            </span>
            :<span className="text-codeline-link">"</span>
            <span id="name-value" className="text-codeline-link">
              {name}
            </span>
            <span className="text-codeline-link">"</span>, <br />
            &nbsp;&nbsp;
            <span id="email" className="tag-name">
              email
            </span>
            :<span className="text-codeline-link">"</span>
            <span id="email-value" className="text-codeline-link">
              {email}
            </span>
            <span className="text-codeline-link">"</span>, <br />
            &nbsp;&nbsp;
            <span id="message" className="tag-name">
              message
            </span>
            :<span className="text-codeline-link">"</span>
            <span id="message-value" className="text-codeline-link">
              {message}
            </span>
            <span className="text-codeline-link">"</span>, <br />
            &nbsp;&nbsp; date:{" "}
            <span className="text-codeline-link">"{currentDate}"</span> <br />
            {"}"}
          </p>
          <br />
          <p>
            <span className="tag-name">
              button.addEventListener
              <span className="text-menu-text">("click"), ()</span>{" "}
              <span className="tag">=&gt;</span>
              <br />
            </span>
            &nbsp;&nbsp;form.send<span className="text-menu-text">(</span>
            message
            <span className="text-menu-text">);</span>
          </p>
        </div>
      </div>
    </>
  );
};

export default FormContent;
