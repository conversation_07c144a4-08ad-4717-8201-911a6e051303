{"name": "portfolio-redesigned", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@emailjs/browser": "^4.4.1", "@next/third-parties": "^15.4.4", "axios": "^1.11.0", "clsx": "^2.1.1", "highlight.js": "^11.11.1", "next": "15.4.4", "react": "19.1.0", "react-dom": "19.1.0", "react-syntax-highlighter": "^15.6.1", "tailwind-merge": "^3.3.1"}, "devDependencies": {"@tailwindcss/postcss": "^4", "@types/highlight.js": "^9.12.4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "tailwindcss": "^4", "typescript": "^5"}}